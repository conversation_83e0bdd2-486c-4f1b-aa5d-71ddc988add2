<?php

namespace frontend\services;

use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\models\ContentTemplate;
use common\models\Filter;
use common\models\FilterGroup;
use common\models\FilterPageSeo;
use common\services\CollegeService;
use frontend\helpers\Url;
use Yii;
use yii\caching\TagDependency;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class FilterService
{
    const YEAR = '2025';

    private static $_fieldMappings = [
        'State' => 'state',
        'City' => 'city',
        'Streams' => 'stream',
        'Courses' => 'course',
        'Program Mode' => 'mode',
        'Exams Accepted' => 'exam',
        'Course Type' => 'courseType',
        'Specialization' => 'specialization',
        'Affiliated By' => 'affiliated_by',
        'Approvals' => 'approvals',
        'Ownership' => 'ownership',
        'Total Fees' => 'fees'
    ];

    public function getStateFilter()
    {
        $query = Filter::find()
            ->with(['filterGroup' => function ($m) {
                return $m->orderBy(['position' => SORT_ASC]);
            }])
            ->where(['status' => Filter::STATUS_ACTIVE])
            ->andWhere(['filter_group_id' => 1]);
        $results = $query->all();

        return $results;
    }

    public function getCityFilter()
    {
        $query = Filter::find()
            ->with(['filterGroup' => function ($m) {
                return $m->orderBy(['position' => SORT_ASC]);
            }])
            ->where(['filter_group_id' => 2])
            ->andWhere(['IS NOT', 'position', null])
            ->andWhere(['status' => Filter::STATUS_ACTIVE])
            ->orderBy(['position' => SORT_ASC])
            ->limit(50);
        $results = $query->all();

        return $results;
    }

    public function getStreamFilter()
    {
        $query = Filter::find()
            ->with(['filterGroup' => function ($m) {
                return $m->orderBy(['position' => SORT_ASC]);
            }])
            ->where(['status' => Filter::STATUS_ACTIVE])
            ->andWhere(['filter_group_id' => 3]);
        $results = $query->all();

        return $results;
    }

    public function getCourseFilter()
    {
        $query = Filter::find()
            ->with(['filterGroup' => function ($m) {
                return $m->orderBy(['position' => SORT_ASC]);
            }])
            ->where(['status' => Filter::STATUS_ACTIVE])
            ->andWhere(['filter_group_id' => 4]);
        $results = $query->all();

        return $results;
    }

    public function getSpecializationFilter()
    {
        $query = Filter::find()
            ->with(['filterGroup' => function ($m) {
                return $m->orderBy(['position' => SORT_ASC]);
            }])
            ->where(['status' => Filter::STATUS_ACTIVE])
            ->andWhere(['filter_group_id' => 5]);
        $results = $query->all();

        return $results;
    }

    public function getProgramModFilter()
    {
        $query = Filter::find()
            ->with(['filterGroup' => function ($m) {
                return $m->orderBy(['position' => SORT_ASC]);
            }])
            ->where(['status' => Filter::STATUS_ACTIVE])
            ->andWhere(['filter_group_id' => 6]);
        $results = $query->all();

        return $results;
    }

    public function getProgramModFilterSpecialization()
    {
        $query = Filter::find()
            ->with(['filterGroup' => function ($m) {
                return $m->orderBy(['position' => SORT_ASC]);
            }])
            ->where(['status' => Filter::STATUS_ACTIVE])
            ->andWhere(['filter_group_id' => 5]);
        $results = $query->all();
        return $results;
    }

    public function getOwnerShipFilter()
    {
        $query = Filter::find()
            ->with(['filterGroup' => function ($m) {
                return $m->orderBy(['position' => SORT_ASC]);
            }])
            ->where(['status' => Filter::STATUS_ACTIVE])
            ->andWhere(['filter_group_id' => 7]);
        $results = $query->all();

        return $results;
    }

    public function getExamFilter()
    {
        $query = Filter::find()
            ->with(['filterGroup' => function ($m) {
                return $m->orderBy(['position' => SORT_ASC]);
            }])
            ->where(['status' => Filter::STATUS_ACTIVE])
            ->andWhere(['filter_group_id' => 8]);
        $results = $query->all();

        return $results;
    }

    public function getcourseTypeFilter()
    {
        $query = Filter::find()
            ->with(['filterGroup' => function ($m) {
                return $m->orderBy(['position' => SORT_ASC]);
            }])
            ->where(['status' => Filter::STATUS_ACTIVE])
            ->andWhere(['filter_group_id' => 9]);
        $results = $query->all();

        return $results;
    }

    public function getAffliatedByFilter()
    {
        $query = Filter::find()
            ->with(['filterGroup' => function ($m) {
                return $m->orderBy(['position' => SORT_ASC]);
            }])
            ->where(['status' => Filter::STATUS_ACTIVE])
            ->andWhere(['filter_group_id' => 10]);
        $results = $query->all();

        return $results;
    }

    public function getTotalFeeFilter()
    {
        $query = Filter::find()
            ->with(['filterGroup' => function ($m) {
                return $m->orderBy(['position' => SORT_ASC]);
            }])
            ->where(['status' => Filter::STATUS_ACTIVE])
            ->andWhere(['filter_group_id' => 11]);
        $results = $query->all();

        return $results;
    }

    public function getApprovalFilter()
    {
        $query = Filter::find()
            ->with(['filterGroup' => function ($m) {
                return $m->orderBy(['position' => SORT_ASC]);
            }])
            ->where(['status' => Filter::STATUS_ACTIVE])
            ->andWhere(['filter_group_id' => 12]);
        $results = $query->all();

        return $results;
    }

    public function getAllFilters()
    {
        $currentPath = Yii::$app->request->url;
        $args = __CLASS__ . '-' . __FUNCTION__;
        if ($currentPath == '/all-colleges') {
            $key = md5(base64_encode(serialize($args)) . $currentPath);
        } else {
            $key = md5(base64_encode(serialize($args)));
        }

        $data = Yii::$app->cache->getOrSet($key, function () use ($currentPath) {
            $topCityList = [];
            // $query = Filter::find()
            //     ->with(['filterGroup' => function ($m) {
            //         return $m->orderBy(['position' => SORT_ASC]);
            //     }])
            //     ->where(['status' => Filter::STATUS_ACTIVE]);

            $results['state'] = $this->getStateFilter();


            if ($currentPath == '/all-colleges') {
                $cityGroup = FilterGroup::find()->select(['id'])->where(['name' => 'City'])->one();

                $topCityList = Filter::find()
                    ->with('filterGroup')
                    ->where(['filter_group_id' => $cityGroup->id])
                    ->andWhere(['IS NOT', 'position', null])
                    ->andWhere(['status' => Filter::STATUS_ACTIVE])
                    ->orderBy(['position' => SORT_ASC])
                    ->limit(50)
                    ->all();

                $results['city'] = $topCityList;
                // $query->andWhere(['<>', 'filter_group_id', $cityGroup->id]);
            } else {
                $results['city'] = $this->getCityFilter();
            }

            $results['stream'] = $this->getStreamFilter();
            $results['course'] = $this->getCourseFilter();
            $results['specialization'] = $this->getProgramModFilterSpecialization();
            $results['programMode'] = $this->getProgramModFilter();
            $results['owner'] = $this->getOwnerShipFilter();
            $results['exam'] = $this->getExamFilter();
            $results['totalFee'] = $this->getTotalFeeFilter();
            $results['courseType'] = $this->getcourseTypeFilter();
            $results['affliatedBy'] = $this->getAffliatedByFilter();
            $results['approval'] = $this->getApprovalFilter();

            return array_merge(
                $results['state'],
                $results['city'],
                $results['stream'],
                $results['course'],
                $results['programMode'],
                $results['owner'],
                $results['exam'],
                $results['totalFee'],
                $results['courseType'],
                $results['affliatedBy'],
                $results['approval'],
                $results['specialization']
            );
            // $results = $query->all();

            //     return $results;
        }, 60 * 60 * 2, new TagDependency(['tags' => 'get-all-filters']));

        return $data;
    }

    public function getFilterValues($searchModel, $currentUrl = '')
    {
        $availableFacets = $searchModel->availableFacets;
        $facetsItems = [];

        foreach ($availableFacets as $facetKey => $facetValues) {
            foreach ($facetValues as $facetValue) {
                if (isset($facetValue['count']) && !empty($facetValue['count'])) {
                    if (!empty($facetValue['_id'])) {
                        $facetsItems[$facetKey][$facetValue['_id']] = $facetValue['count'];
                    }
                }
            }
        }

        $items = [];
        $filterValues = $this->getAllFilters();
        foreach ($filterValues as $filterValue) {
            if (isset(self::$_fieldMappings[$filterValue->filterGroup->name]) && isset($facetsItems[self::$_fieldMappings[$filterValue->filterGroup->name]])) {
                if (isset($facetsItems[self::$_fieldMappings[$filterValue->filterGroup->name]][$filterValue->slug])) {
                    // $relatedUrl = $this->getRelatedUrl($searchModel->selectedFilters, $filterValue);
                    // $isLinked = ($relatedUrl['relatedUrl'] ?? '') != (Url::toTestUrl() . ltrim($currentUrl, '/'));
                    // if ($filterValue->filterGroup->url_position != 0) {
                    // $html = '<a href="' . (($isLinked) ? ($relatedUrl['relatedUrl'] ?? '') : 'javascript:;') . '"' . (isset($relatedUrl['title']) ? 'title="' . $relatedUrl['title'] . '"' : '') . '>';
                    $html = '<label for="' . $filterValue->slug . '">' . $filterValue->name . ' (' . $facetsItems[self::$_fieldMappings[$filterValue->filterGroup->name]][$filterValue->slug] . ')' . '</label>';
                    // $html .= '</a>';
                    // } else {
                    //     $html = '<label for="' . $filterValue->slug . '">' . $filterValue->name . ' (' . $facetsItems[self::$_fieldMappings[$filterValue->filterGroup->name]][$filterValue->slug] . ')' . '</label>';
                    // }
                    $items[$filterValue->filterGroup->name][$filterValue->slug] = $html;
                }
            }
        }

        return $this->sortFilters($items, $facetsItems);
    }

    public function sortFilters($items, $facetItems)
    {
        $fieldMapping = [];
        foreach (self::$_fieldMappings as $key => $mapping) {
            $fieldMapping[$mapping] = $key;
        }

        $fgroupItems = [];
        foreach ($facetItems as $facetKey => $facetItem) {
            foreach ($facetItem as $k => $val) {
                if (isset($fieldMapping[$facetKey])) {
                    if (isset($items[$fieldMapping[$facetKey]][$k])) {
                        $fgroupItems[$fieldMapping[$facetKey]][$k] = $items[$fieldMapping[$facetKey]][$k];
                    }
                }
            }
        }

        return $fgroupItems;
    }

    /**
     * Service Method for Related Url of all the available filters.
     *
     * @param string| $url          Exisitng domain main URL.
     * @param string| $path         Exisitng domain first priority path.
     * @param string| $filter       Each filter Name.
     * @param string| $filterGroup  Each filterGroup Name.
     *
     * @return string| $newUrl New Generated Related Url for each filter option available.
     */
    public function getRelatedUrl($selectedFilters, $checkbox)
    {
        $isExam = false;
        if (!isset($checkbox->filterGroup)) {
            return '';
        }
        // eliminate checkbox group from selected Fields
        foreach ($selectedFilters as $key => $selectedFilter) {
            if (isset($selectedFilter['filterGroup_name']) && $selectedFilter['url_position'] == $checkbox->filterGroup->url_position) {
                unset($selectedFilters[$key]);
            }
        }

        if ($checkbox->filterGroup->name == 'Exams Accepted') {
            return $this->getExamUrl($selectedFilters, $checkbox);
        }

        if ($checkbox->filterGroup->url_position == 2 && empty($selectedFilters)) {
            $seoInfo = $this->getSeoInfo('all-colleges', $checkbox->slug, null, false, true);
            return [
                'relatedUrl' => Url::toTestUrl() . 'all-colleges/' . $checkbox->slug,
                'title' => $seoInfo['h1'] ?? '',
            ];
        }

        if ($checkbox->filterGroup->url_position == 1 && empty($selectedFilters)) {
            $seoInfo = $this->getSeoInfo($checkbox->slug, null, null, false, true);
            return [
                'relatedUrl' => Url::toTestUrl() . $checkbox->slug . '-colleges',
                'title' => $seoInfo['h1'] ?? '',
            ];
        }

        if ($checkbox->filterGroup->url_position == 0) {
            return '';
        }

        $eligibleFields[$checkbox->filterGroup->url_position] = [
            'slug' => $checkbox->slug
        ];

        $selectedFilters = ArrayHelper::index($selectedFilters, 'rule.priority', null);
        arsort($selectedFilters);
        $selectedFilters = array_slice($selectedFilters, 0, 1);

        if (isset($selectedFilters[0])) {
            if ($selectedFilters[0]['mapped_field'] == 'exam') {
                $isExam = true;
            }

            $eligibleFields[$selectedFilters[0]['url_position']] = [
                'slug' => $selectedFilters[0]['slug']
            ];
        }

        $seoInfo = $this->getSeoInfo(($eligibleFields[1]['slug'] ?? 'all-colleges'), (isset($eligibleFields[2]['slug']) ?  $eligibleFields[2]['slug'] : ''), null, $isExam, true);
        if ($isExam) {
            $finalSlug = Url::toTestUrl() . 'colleges-accepting-' . ($eligibleFields[1]['slug']) . '-score-in-' . (isset($eligibleFields[2]['slug']) ?  $eligibleFields[2]['slug'] : 'india');
        } else {
            $finalSlug = Url::toTestUrl() . ($eligibleFields[1]['slug'] ?? 'all') . '-colleges' . (isset($eligibleFields[2]['slug']) ? '/' . $eligibleFields[2]['slug'] : '');
        }

        return [
            'relatedUrl' => $finalSlug,
            'title' => $seoInfo['h1'] ?? '',
        ];
    }

    public function getExamUrl($selectedFilters, $checkbox)
    {
        if (!empty($selectedFilters)) {
            $selectedFilters = ArrayHelper::index($selectedFilters, 'filterGroup_name');
        }
        if (isset($selectedFilters['City'])) {
            $seoInfo = $this->getSeoInfo($checkbox->slug, $selectedFilters['City']['slug'], null, true, true);
            return [
                'relatedUrl' => Url::toTestUrl() . 'colleges-accepting-' . $checkbox->slug . '-score-in-' . $selectedFilters['City']['slug'],
                'title' => $seoInfo['h1'] ?? '',
            ];
        }
        if (isset($selectedFilters['State'])) {
            $seoInfo = $this->getSeoInfo($checkbox->slug, $selectedFilters['State']['slug'], null, true, true);
            return [
                'relatedUrl' => Url::toTestUrl() . 'colleges-accepting-' . $checkbox->slug . '-score-in-' . $selectedFilters['State']['slug'],
                'title' => $seoInfo['h1'] ?? '',
            ];
        }

        $seoInfo = $this->getSeoInfo($checkbox->slug, null, null, true, true);
        return [
            'relatedUrl' => Url::toTestUrl() . 'colleges-accepting-' . $checkbox->slug . '-score-in-' . 'india',
            'title' => $seoInfo['h1'] ?? '',
        ];
    }

    public function generateUrl($params)
    {
        if (strpos($_SERVER['HTTP_REFERER'], 'approved-colleges/india') !== false) {
            $collegesApproved = true;
        } else {
            $collegesApproved = false;
        }

        $finalUrl = '/';
        $items = [];

        if (!isset($params->bodyParams['CollegeSearch'])) {
            return '/all-colleges';
        }

        if (empty($params = $params->bodyParams['CollegeSearch'])) {
            return '/all-colleges';
        }

        //affiliated colleges url
        if (!empty($params['affiliated_by']) && $collegesApproved == false) {
            if (!empty($params['affiliated_by'][0])) {
                $finalUrl .= 'college/colleges-under-' . $params['affiliated_by'][0];
            }

            return self::getAffiliatedAndApprovedUrls($finalUrl, $params, 'affiliated_by');
        }

        //approved colleges url
        if (!empty($params['approvals'])) {
            if (!empty($params['approvals'][0])) {
                $finalUrl .= $params['approvals'][0] . '-approved-colleges/india';
            }

            return self::getAffiliatedAndApprovedUrls($finalUrl, $params, 'approvals');
        }

        foreach ($params as $key => $value) {
            if (!empty($value)) {
                foreach ($value as $k => $v) {
                    $items[] = $v;
                }
            }
        }

        $filters = Filter::find()->where(['in', 'slug', $items])->with('filterGroup')->all();
        $filters = ArrayHelper::index($filters, null, 'filterGroup.mapped_field');
        if (!$filters) {
            return '/all-colleges';
        }

        $filterData = [];
        foreach ($filters as $key => $value) {
            foreach ($value as $filter) {
                $urlPosition = $filter->filterGroup->url_position;
                $rule = Json::decode($filter->filterGroup->rule);
                $filterData[$urlPosition][] = [
                    'slug' => $key,
                    'urlPosition' => $urlPosition,
                    'priority' => $rule['priority']
                ];
            }
        }

        arsort($filterData);
        $urlPositionData = [];
        foreach ($filterData as $key => $items) {
            if (count($items) > 1) {
                $allItems = ArrayHelper::index($items, null, 'priority');
                $firstPriority = array_key_exists('1', $allItems);
                if ($firstPriority) {
                    foreach ($allItems as $key => $values) {
                        foreach ($values as $k => $val) {
                            if ($val['priority'] == 1) {
                                $urlPositionData[$val['urlPosition']][$val['priority']][] = $val;
                            }
                        }
                    }
                } else {
                    foreach ($allItems as $key => $values) {
                        foreach ($values as $k => $val) {
                            $urlPositionData[$val['urlPosition']][$val['priority']][] = $val;
                        }
                    }
                }
            } else {
                $urlPositionData[$items[0]['urlPosition']][$items[0]['priority']][] = $items[0];
            }
        }

        $finalUrlParams = [];
        foreach ($urlPositionData as $key => $values) {
            foreach ($values as $value) {
                $params = [];
                foreach ($value as $val) {
                    if (!in_array($val['slug'], $params)) {
                        $params[] = $val['slug'];
                    }
                }
            }
            $finalUrlParams[$key] = $params;
        }

        $firstfinalUrl = !empty($finalUrlParams[1]) ? $this->getFirstFinalUrl($filters, $finalUrlParams[1]) : '';
        $secondfinalUrl = !empty($finalUrlParams[2]) ? $this->getSecondFinalUrl($filters, $finalUrlParams[2]) : '';
        $queryParamsUrl = !empty($finalUrlParams[0]) ? $this->getQueryUrl($filters, $finalUrlParams[0]) : '';

        if (isset($filters['exam'])) {
            if (strpos($firstfinalUrl, '?')) {
                $firstfinalUrl = explode('?', $firstfinalUrl);
                $examUrlFirstPart = preg_replace('/\W\w+\s*(\W*)$/', '$1', $firstfinalUrl[0]);
            } else {
                $examUrlFirstPart = preg_replace('/\W\w+\s*(\W*)$/', '$1', $firstfinalUrl);
            }

            $examUrlSecondPart = ltrim($secondfinalUrl, '/');
            $url = (!empty($examUrlFirstPart) ? $examUrlFirstPart : 'all-colleges') . '-' . (!empty($examUrlSecondPart) ? $examUrlSecondPart : 'india');
        } else {
            $url = (!empty($firstfinalUrl) ? $firstfinalUrl : 'all-colleges') . (!empty($secondfinalUrl) ? $secondfinalUrl : '');
        }

        if (strpos($url, '?')) {
            $finalUrl .= !empty($queryParamsUrl) ? $url . '&' . $queryParamsUrl : $url;
        } else {
            $finalUrl .= !empty($queryParamsUrl) ? $url . '?' . $queryParamsUrl : $url;
        }

        if (is_array($firstfinalUrl) && !empty($firstfinalUrl[1])) {
            if (strpos($finalUrl, '?')) {
                $finalUrl .= !empty($firstfinalUrl[1]) ? '&' . $firstfinalUrl[1] : '';
            } else {
                $finalUrl .= !empty($firstfinalUrl[1]) ? '?' . $firstfinalUrl[1] : '';
            }
        }

        return $finalUrl;
    }

    private function getFirstFinalUrl($filters, $group)
    {
        $finalUrl = '';
        $queryString = '';
        $urlParams = [];

        foreach ($group as $key => $value) {
            $url = '';
            $filterData = $filters[$value];
            $count = count($filterData);
            if (strtolower($value) == 'exam') {
                $url .= 'colleges-accepting-' . $filterData[0]['slug'] . '-score-in-india';
                if ($count > 1) {
                    for ($i = 1; $i < $count; $i++) {
                        $urlParams[] = $filterData[$i]['slug'];
                    }

                    if (!empty($urlParams)) {
                        if (!empty($queryString)) {
                            $queryString .= '&' . $value . '=' . implode(',', $urlParams);
                        } else {
                            $queryString .= $value . '=' . implode(',', $urlParams);
                        }
                    }
                }
            } else {
                $url .= $filterData[0]['slug'] . '-colleges';
            }
        }

        if (!empty($url) && !empty($queryString)) {
            $finalUrl = $url . '?' . $queryString;
        } else {
            $finalUrl = $url;
        }

        return $finalUrl ?? 'all-colleges';
    }

    private function getSecondFinalUrl($filters, $group)
    {
        $url = '';
        $finalUrl = '';
        $queryString = '';
        $urlParams = [];

        foreach ($group as $key => $value) {
            $filterData = $filters[$value];
            $count = count($filterData);
            $url .= '/' . $filterData[0]['slug'];

            if ($count > 1) {
                for ($i = 1; $i < $count; $i++) {
                    $urlParams[] = $filterData[$i]['slug'];
                }

                if (!empty($urlParams)) {
                    if (!empty($queryString)) {
                        $queryString .= '&' . $value . '=' . implode(',', $urlParams);
                    } else {
                        $queryString .= $value . '=' . implode(',', $urlParams);
                    }
                }
            }
        }

        if (!empty($url) && !empty($queryString)) {
            $finalUrl = $url . '?' . $queryString;
        } else {
            $finalUrl = $url;
        }

        return $finalUrl;
    }

    private function getQueryUrl($filters, $group)
    {
        $queryString = '';
        foreach ($group as $key => $value) {
            $filterData = $filters[$value];
            $urlParams = [];

            foreach ($filterData as $filter) {
                $urlParams[] = $filter['slug'];
            }

            if (!empty($urlParams)) {
                if (!empty($queryString)) {
                    $queryString .= '&' . $value . '=' . implode(',', $urlParams);
                } else {
                    $queryString .= $value . '=' . implode(',', $urlParams);
                }
            }
        }

        return $queryString;
    }

    private function getAffiliatedAndApprovedUrls($finalUrl, $params, $filter)
    {
        $items = [];

        foreach ($params as $key => $value) {
            if (!empty($value)) {
                foreach ($value as $k => $v) {
                    if ($key == $filter) {
                        continue;
                    }
                    $items[$key][] = $v;
                }
            }
        }

        // Concatenate array values into a comma-separated string
        foreach ($items as $key => $value) {
            if (is_array($value)) {
                $filterValues[$key] = implode(',', $value);
            }
        }

        $query_string = !empty($filterValues) ? http_build_query($filterValues) : '';
        $query_string = !empty($query_string) ? str_replace('%2C', ',', $query_string) : '';
        $finalUrl .=  !empty($query_string) ? '?' . $query_string : '';

        return $finalUrl;
    }

    public function getFilterBySlug($slug)
    {
        $key = __CLASS__ . __FUNCTION__ . md5(base64_encode(serialize($slug)));
        ;

        return Yii::$app->cache->getOrSet($key, function () use ($slug) {
            return Filter::find()->where(['slug' => $slug])->one();
        }, 60 * 60 * 2);
    }

    /**
     * Function for finding Top Content and Other SEO params.
     *
     * @param string| $filter   A Slug of applied filter.
     * @param string| $location A Slug of applied filter which contains either State or City.
     * @param bool| $isExamFilter Whether applied filter is of Exam or not.
     * @param int| $totalColleges No of colleges available after applying the filter.
     *
     * @return array|[] Returns SEO and TOP Content Data in an array.
     */
    public function getSeoInfo($filter, $location = null, $totalColleges = 0, $isExamFilter = false, $isOnlyH1 = false)
    {
        $slugToFind = 'all-colleges';
        $filterPageSeo = [];
        if ($filter == $slugToFind) {
            $filter = '';
        }

        if (!empty($filter) && ($filter != $slugToFind)) {
            $filterGroup = $this->getFilterBySlug($filter);
            $slugToFind = $filter . '-colleges';
        }
        if (isset($filterGroup) && !empty($filterGroup)) {
            if ($filterGroup->filterGroup->mapped_field == 'exam') {
                $isExamFilter = true;
                $slugToFind = 'colleges-accepting-' . $filter . '-score-in-' . ($location ?? 'india');
            }
        }

        if ($location && !$isExamFilter) {
            $slugToFind .= '/' . $location;
        }
        if (!$isOnlyH1) {
            $filterPageSeo = FilterPageSeo::find()->where(['slug' => $slugToFind])->active()->one();
        }
        if ($location) {
            $location = ucwords(str_replace('-', ' ', $location));
        }

        if ($filterPageSeo && !$isOnlyH1) {
            if ($isExamFilter) {
                $defaultDescription = 'Get the complete list of Top ' . (($totalColleges > 1) ? $totalColleges . ' Colleges' : $totalColleges . ' College') . ' In ' . ($location ?? 'India') . ' accepting' . (($filter) ? ' ' . ($filterGroup->name ?? ucwords($filter)) : '') . ' which includes student reviews, rankings, courses offered, fees, admission, and placements.';
            } else {
                $defaultDescription = 'Find Top' . (' ' . $filterGroup->name ?? '') . ' Colleges in ' . ($location ?? 'India') . ' based on  ranking with details on courses, fees, placements, admission, reviews, cut off, and latest news.';
            }
            if (empty($h1 = $filterPageSeo->h1)) {
                $h1 = 'Top' . (!empty($filterGroup->name) ? ' ' . $filterGroup->name . ' Colleges' : ' Colleges') . ' in ' . ($location ?? 'India');
            }

            return [
                'h1' => $h1 . ' ' . self::YEAR,
                'title' => $h1 . ' ' . self::YEAR . ': ' . 'Ranking, Courses, Fees & Placements',
                'meta_title' => $filterPageSeo->meta_title,
                //'meta_description' => $filterPageSeo->meta_description ?? $defaultDescription,
                'meta_description' => !empty($filterPageSeo->meta_description) ? $filterPageSeo->meta_description : $defaultDescription,
                'content' => $filterPageSeo->content,
            ];
        } else {
            if ($filter) {
                $filter = ucwords(explode('-', $filter)[0]);
            }

            $filterName = '';
            if (isset($filterGroup) && !empty($filterGroup)) {
                $filterName = ' ' . $filterGroup->name;
            }

            $defaultDescription = 'Find Top' . $filterName . ' Colleges in ' . ($location ?? 'India') . ' based on  ranking with details on courses, fees, placements, admission, reviews, cut off, and latest news.';

            if ($isExamFilter) {
                $defaultDescription = 'Get the complete list of Top ' . (($totalColleges > 1) ? $totalColleges . ' Colleges' : $totalColleges . ' College') . ' In ' . ($location ?? 'India') . ' accepting' . (($filter) ? ' ' . ($filterGroup->name ?? ucwords($filter)) : '') . ' which includes student reviews, rankings, courses offered, fees, admission, and placements.';
                return [
                    'h1' => 'Colleges Accepting ' . ($filterGroup->name ?? ucwords($filter)) . ' Score in ' . ($location ?? 'India') . ' ' . self::YEAR,
                    'title' => 'Colleges Accepting ' . ($filterGroup->name ?? ucwords($filter)) . ' Score in ' . ($location ?? 'India') . ' ' . self::YEAR,
                    'meta_description' => $defaultDescription,
                ];
            }
            if (empty($filter) && empty($location)) {
                $h1 = '';
            } else {
                $h1 = 'Top' . (!empty($filterGroup->name) ? ' ' . $filterGroup->name . ' Colleges' : ' Colleges') . ' in ' . ($location ?? 'India') . ' ' . self::YEAR;
            }

            return [
                'h1' => $h1,
                'title' => 'Top' . (!empty($filterGroup->name) ? ' ' . $filterGroup->name . ' Colleges' : ' Colleges') . ' in ' . ($location ?? 'India') . ' ' . self::YEAR . ': ' . 'Ranking, Courses, Fees & Placements',
                'meta_description' => $defaultDescription,
            ];
        }

        return [];
    }

    //get filter page content template
    public function getAllListingTemplate()
    {
        return ContentTemplate::find()
            ->where(['entity_type' => 'all_listing'])
            ->andWhere(['page' => 'all-colleges'])
            ->andWhere(['status' => ContentTemplate::STATUS_ACTIVE])
            ->one();
    }

    public function getContentTemplateData($searchModel, $totalCount, $templateContent)
    {
        $availableFacets = $searchModel->availableFacets;
        $facetsItems = [];
        $replacements = [];

        // Build facetsItems: key => [_id => count]
        foreach ($availableFacets as $facetKey => $facetValues) {
            foreach ($facetValues as $facetValue) {
                if (!empty($facetValue['count']) && !empty($facetValue['_id'])) {
                    $facetsItems[$facetKey][$facetValue['_id']] = $facetValue['count'];
                }
            }
        }

        $wantedKeys = [
            '{Delhi_Ncr_College_Count}',
            '{Haryana_College_Count}',
            '{Karnataka_College_Count}',
            '{Maharashtra_College_Count}',
            '{Uttar_Pradesh_College_Count}',
            '{Tamil_Nadu_College_Count}',
        ];

        // Process state counts only for wanted keys
        if (!empty($facetsItems['state']) && is_array($facetsItems['state'])) {
            foreach ($facetsItems['state'] as $stateName => $count) {
                $placeholder = '{' . ucwords(str_replace('-', '_', $stateName), '_') . '_College_Count}';
                if (in_array($placeholder, $wantedKeys, true)) {
                    $replacements[$placeholder] = number_format($count);
                }
            }
        }

        // Process stream counts
        if (!empty($facetsItems['stream']) && is_array($facetsItems['stream'])) {
            foreach ($facetsItems['stream'] as $streamName => $count) {
                $placeholder = '{' . ucwords(str_replace('-', '_', $streamName), '_') . '_College_Count}';
                $replacements[$placeholder] = number_format($count);
            }
        }

        // Prepare entrance exam names (top 10)
        $exams = (!empty($facetsItems['exam']) && is_array($facetsItems['exam']))
            ? array_slice($facetsItems['exam'], 0, 10)
            : [];
        $examNames = '';

        if (!empty($exams)) {
            $formattedKeys = array_map(function ($key) {
                return ucwords(str_replace('-', ' ', $key));
            }, array_keys($exams));
            $examNames = implode(', ', $formattedKeys);
        }

        // Get top NIRF colleges
        $nirfColleges = (new CollegeService())->getTopNirfColleges();
        $index = 1;
        foreach ($nirfColleges['ranks'] as $collegeName => $rank) {
            if ($index > 10) {
                break;
            }
            $replacements["{College_NIRF_Name_$index}"] = $collegeName;
            $replacements["{NIRF_Rank_$index}"] = $rank;

             // Replace the slug as well
            $slug = $nirfColleges['slug'][$collegeName];  // Get the slug for the current college
            $replacements["{College_NIRF_Name_$index" . '_URL}'] = '/college/' . $slug;  // Add slug replacement
            $index++;
        }

        // Course-wise college counts
        if (!empty($facetsItems['course']) && is_array($facetsItems['course'])) {
            foreach (CollegeHelper::$courseMap as $courseId => $courseKey) {
                $replacementKey = '{' . strtoupper(str_replace(' ', '_', $courseKey)) . '_College_Count}';
                $count = $facetsItems['course'][$courseKey] ?? 0;
                $replacements[$replacementKey] = number_format($count);
            }
        }

        // Stream counts: public/private + top colleges
        $streamIds = array_keys(CollegeHelper::$streamMap);
        $streamCounts = (new CollegeService())->getAllStreamPublicPrivateCounts($streamIds);
        $allStreamsTopColleges = (new CollegeService())->getTopNirfCollegesByStream($streamIds, 5);
        $streamReplacements = (new CollegeService())->formatTopCollegesPerStreamForTemplate($allStreamsTopColleges);
        $replacements = array_merge($replacements, $streamReplacements);

        foreach (CollegeHelper::$streamMap as $id => $name) {
            $publicKey  = '{Public_' . ucfirst($name) . '_College_Count}';
            $privateKey = '{Private_' . ucfirst($name) . '_College_Count}';
            $replacements[$publicKey]  = number_format($streamCounts[$id]['public_count'] ?? 0);
            $replacements[$privateKey] = number_format($streamCounts[$id]['private_count'] ?? 0);
        }


        // Final static replacements
        $replacements = array_merge($replacements, [
            '{Latest_Year}' => date('Y'),
            '{Colleges_Count}' => $totalCount ? number_format($totalCount) : 0,
            '{Public_College_Count}' => $facetsItems['ownership']['public'] ?? 0,
            '{Private_Colleges_Count}' => $facetsItems['ownership']['private'] ?? 0,
            '{Entrance_Exam_Names}' => $examNames,
            '{Top_10_College_Names}' => $nirfColleges['collegeNames'],
        ]);

        $contentWithReplacements = strtr($templateContent, $replacements);

        // First remove empty table rows
        $contentCleanRows = (new ContentHelper)->removeEmptyPlaceholderRows($contentWithReplacements);

        // Then remove empty sections (h2 + p + table) if table has no real data
        $contentCleanSections = (new ContentHelper)->removeEmptyTableSections($contentCleanRows);

        return $contentCleanSections;
    }
}
