<?php

use common\helpers\BoardHelper;
use common\helpers\ContentHelper;
use common\models\Board;
use common\models\LiveUpdate;
use frontend\assets\AppAsset;
use frontend\helpers\Ad;
use frontend\helpers\Url;
use yii\helpers\ArrayHelper;
use common\helpers\CollegeHelper;
use common\helpers\DataHelper;
use common\services\PopularCollegeService;
use frontend\helpers\Freestartads;

//util
if (empty($pageContent->author)) {
    $author = $pageContent->defaultuser;
} else {
    $author = $pageContent->author;
}
$currentUrl = Url::base(true) . Url::current();
$canonicalUrl = Url::base(true) . '/' . \Yii::$app->request->getPathInfo();
$authorImage = !empty($author->profile->image) ? Yii::getAlias('@profileDPFrontend') . '/' . $author->profile->image : '/yas/images/usericon.png';
$subMenu = array_column(ArrayHelper::toArray($pages), 'page_slug');
$isMobile = \Yii::$app->devicedetect->isMobile();
$assetUrl = Yii::getAlias('@getmyuniExamAsset/');
$displayName = $board->display_name;
if (empty($displayName)) {
    $displayName = ucwords(str_replace('-', ' ', $boardSlug));
}

$newDefaultSeoInfo = ContentHelper::getBoardSeoInfo($displayName, $board->state_id, $pageContent->page_slug, ($type == '' ? '' : $type));
$title = BoardHelper::parseBoardContent($pageContent->meta_title);
$h1 = BoardHelper::parseBoardContent($pageContent->h1);
if (empty($h1)) {
    $h1 = $defaultmetaContent['h1'] ??  $newDefaultSeoInfo[$board->level];
}

if (empty($title)) {
    $title = $defaultmetaContent['title'] ??  $newDefaultSeoInfo[$board->level];
}

if (empty($type)) {
    $defaultSeoInfo = $defaultmetaContent ?? BoardHelper::getBoardDefaultSeoInfo($displayName, $pageName);
} else {
    $defaultSeoInfo =  $defaultmetaContent ?? BoardHelper::getBoardDefaultSubPageSeoInfo($displayName, $pageName, $type);
}
//Board Date
if (!empty($board->dates[0]) || !empty($board->dates[1])) {
    $dates = [];
    foreach ($board->dates as $value) {
        if ($value['name'] != '-' && !empty($value['start-date'])) {
            $dates[$value['name']] = [
                'date' => $value['start-date'] . (empty($value['end-date']) ? '' : ' to ' . $value['end-date']),
                'type' => $value['type'],
            ];
        }
    }
}

$menuLinks = $menuOrder ? $menuOrder : $allBoardPages;

//title
$this->title = empty($title) ? ($defaultSeoInfo['title'] ?? '') : ($title ?? '');
$this->context->description = empty($pageContent->meta_description) ? ($defaultSeoInfo['description'] ?? '') : BoardHelper::parseBoardContent($pageContent->meta_description);
$this->context->ogImage = Yii::getAlias('@boardLogoFrontend/') . $board->logo;
// breadcrumbs
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Boards'), 'url' => [Url::toBoards(DataHelper::getLangCode($board->lang_code))], 'title' => 'Boards'];

$boardSlug = (!empty(BoardHelper::$redirectBoards[$board->slug])) ? BoardHelper::$redirectBoards[$board->slug] : $board->slug;

if ($pageName != 'overview') {
    $this->params['breadcrumbs'][] = ['label' => Yii::t('app', $displayName), 'url' => [Url::toBoardDetail($boardSlug, DataHelper::getLangCode($board->lang_code))], 'title' => $displayName];
}
if (empty($type)) {
    $this->params['breadcrumbs'][] = $faqPageName =  $pageName == 'overview' ? Yii::t('app', $displayName) : (($pageContent->page) ? Yii::t('app', $pageContent->page) : ucwords(str_replace('-', ' ', $pageContent->slug)));
} else {
    $questionPaperUrl = Url::toBoardDetail($boardSlug, DataHelper::getLangCode($board->lang_code), $type);
    $breadcrumbAllowed = ['question-paper', 'books', 'syllabus', 'answer-key'];
    if (in_array($type, $breadcrumbAllowed)) {
        $this->params['breadcrumbs'][] = $faqPageName = ['label' => Yii::t('app', ucwords(str_replace('-', ' ', $type))), 'url' => [$questionPaperUrl], 'title' => ucwords(str_replace('-', ' ', $type))];
        $this->params['breadcrumbs'][] = $faqPageName = Yii::t('app', ucwords(str_replace('-', ' ', $pageName)));
    } else {
        $allowedParent = ['exam-pattern', 'previous-year-question-papers', 'sample-papers'];
        if (in_array($type, $allowedParent)) {
            $this->params['breadcrumbs'][] = $faqPageName = ['label' => Yii::t('app', ucwords(str_replace('-', ' ', $type))), 'url' => [$questionPaperUrl], 'title' => ucwords(str_replace('-', ' ', $type))];
            $this->params['breadcrumbs'][] = $faqPageName = Yii::t('app', $displayName) . ' ' . Yii::t('app', ucwords(str_replace('-', ' ', $pageName)) . ' ' . ucwords(str_replace('-', ' ', $type)));
        } else {
            $this->params['breadcrumbs'][] = $faqPageName = ['label' => Yii::t('app', ucwords(str_replace('-', ' ', $type))), 'url' => [$questionPaperUrl], 'title' => ucwords(str_replace('-', ' ', $type))];
            $this->params['breadcrumbs'][] = $faqPageName = Yii::t('app', $displayName) . ' ' . Yii::t('app', ucwords(str_replace('-', ' ', $pageName)));
        }
    }
}
$dropDownType = $type;
// page specific assets
// $this->registerCssFile(Yii::$app->params['cssPath'] . 'board.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'board-detail.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');

$autoPopUpTexts = array_filter(array_column($dynamicCta, 'auto_pop_up_text'));
$finalAutoPopUpText = !empty($autoPopUpTexts) ? array_values($autoPopUpTexts)[0] : '';
$autoPopUpTitle = array_filter(array_column($dynamicCta, 'auto_pop_up_title'));
$finalAutoPopUpTitle = !empty($autoPopUpTexts) ? array_values($autoPopUpTitle)[0] : '';

//gmu params
$this->params['entity'] = Board::ENTITY_BOARD;
$this->params['entity_id'] = $board->id ?? 0;
$this->params['entity_name'] = $board->name ?? '';
$this->params['entitySlug'] = $boardSlug ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $board->display_name ?? '';
$this->params['entity_subpage_name'] = empty($type) ? $pageContent->page_slug : $pageContent->id;
$this->params['pageName'] = $pageName ?? '';
$this->params['canonicalUrl'] = $canonicalUrl;
$this->params['board_level'] = !empty($board->level) ? $board->level : '';
$this->params['auto_pop_up_text'] = $finalAutoPopUpText;
$this->params['auto_popup_form_title'] = $finalAutoPopUpTitle;

$this->params['streamWebengage'] =  'Other';
$this->params['levelWebengage'] =  $board->level ?  DataHelper::getConstantList('LEVEL', Board::class, $board->level) : null;
if (!empty($board->exams)) {
    $boardExam = '';
    foreach ($board->exams as $exam) {
        $boardExam .= $exam->name . ',';
    }
    $this->params['examWebengage'] =  $boardExam;
} else {
    $this->params['examWebengage'] = 'other';
}

//schema
if (!empty($faqs)) {
    $this->params['schema1'] = CollegeHelper::faqSchema($faqs);
}
if (!empty($board)) {
    $this->params['schema'] = \yii\helpers\Json::encode([[
        '@context' => 'http://schema.org',
        '@type' => 'Article',
        'mainEntityOfPage' => [
            '@type' => 'WebPage',
            '@id' => $canonicalUrl,
        ],
        'headline' => $this->title ?? '',
        'image' => !empty($board->logo) ? [
            DataHelper::s3Path(null, 'board_genral', 'path') . '/' . $board->logo
        ] : '',
        'datePublished' => !empty($pageContent->created_at) ? date(DATE_ATOM, strtotime($pageContent->created_at)) : '',
        'dateModified' => !empty($pageContent->updated_at) ? date(DATE_ATOM, strtotime($pageContent->updated_at)) : '',
        'author' => [
            '@type' => 'Person',
            'name' => !empty($author) ? $author->name : ''
        ],
        'publisher' => [
            '@type' => 'Organization',
            'name' => 'Getmyuni',
            'logo' => [
                '@type' => 'ImageObject',
                'url' => Yii::$app->params['gmuLogo']
            ],
        ],
        'description' => $this->context->description ?? '',
    ]], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
}

//code for translation of boards
if (!empty($translation_data)) {
    if (!empty($translation_data[0]['lang'])) {
        $msg = 'Switch to Hindi';
    } else {
        $msg = 'Switch to English';
    }
    if (!empty($translation_data[0]['page']) && $translation_data[0]['page'] == 'overview') {
        $url = Url::toBoardDetail($translation_data[0]['slug'], $translation_data[0]['lang']);
    } else {
        $url = Url::toBoardDetail($translation_data[0]['slug'], $translation_data[0]['lang'], $translation_data[0]['page'] ?? '');
    }
    if (!empty($translation_data[0]['lang']) && $translation_data[0]['lang'] == 'hi') {
        $this->registerLinkTag(['href' =>  Url::base(true) . $url, 'rel' => 'alternate', 'hreflang' => $translation_data[0]['lang']]);
    } elseif (!empty($translation_data[0]['cu_lang']) && $translation_data[0]['cu_lang'] == 'hi') {
        $this->registerLinkTag(['href' =>  Url::base(true) . '/hi' . $url, 'rel' => 'alternate', 'hreflang' => 'hi']);
    }
    $defaultUrl = str_replace('/hi', '', $url);
    $this->registerLinkTag(['href' => Url::base(true) . $defaultUrl, 'rel' => 'alternate', 'hreflang' => 'en']);
    $this->registerLinkTag(['href' =>   Url::base(true) . $defaultUrl, 'rel' => 'alternate', 'hreflang' => 'x-default']);
} else {
    $url = '#';
}

$popularCollegesWidget = '<div class="popular-colleges-widget" data-level-id="" data-state-id="' . $board->state_id . '" data-stream-id=""></div>';

$h2Text = ContentHelper::getGenerateHtml($content, '', '', $popularCollegesWidget);
$content = $h2Text['content'];
$excludedSubpage = ['sample-papers', 'question-paper', 'previous-year-question-papers', 'solved-question-papers'];
$year = (date('Y') == '2025') ? date('Y') : '2025';
?>

<div class="">
    <header class="boardsheroSection commonHeroSection">
        <div class="row">
            <div class="col-md-12">
                <div class="row heroHeader">
                    <div class="imgContainer">
                        <img class="<?= $board->logo  ? '' : 'defaultLogoImage'; ?>" src="<?= $board->logo ? DataHelper::s3Path(null, 'board_genral', 'path') . '/' . $board->logo :  Url::defaultCollegeLogo(); ?>" alt="">
                    </div>
                    <div class="headingContainer">
                        <h1><?= empty($h1) ? ($defaultSeoInfo['h1'] ?? '') : ($h1 ?? '') ?></h1>
                    </div>
                </div>


            </div>
            <div class="col-md-12 second-row-date">
                <?php if (!empty($dates)): ?>
                    <div class="examDates row m-0 col-md-7">
                        <div class="row helpfulInfo">
                            <?php foreach ($dates as $key => $value): ?>
                                <div class="helpfuItem">
                                    <span class="spriteIcon calenderIcon"></span>
                                    <?php if (!empty($key)): ?>
                                        <span><?= $key == '-' ? '' : BoardHelper::$boardDates[$key] . ':' ?></span>
                                    <?php endif; ?>
                                    <?php $type = !empty($value['type'] == Board::MODE_TENTATIVE) ? ' (Tentative)' : '';
                                    $date = explode('to', $value['date']); ?>
                                    <?php if (!empty($date[0]) && !empty($date[1])): ?>
                                        <span><?= date('d', strtotime($date[0])) . ' ' . date('M', strtotime($date[0])) . " '" . date('y', strtotime($date[0])) . (empty($date[1]) ? '' : ' to ' . date('d', strtotime($date[1])) . ' ' . date('M', strtotime($date[1])) . " '" . date('y', strtotime($date[1]))) . $type ?></span>
                                    <?php else: ?>
                                        <span><?= date('d', strtotime($date[0])) . ' ' . date('M', strtotime($date[0])) . " '" . date('y', strtotime($date[0])) . $type ?></span>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="col-md-5  authorInfoAndTranslateBtn">
                        <div class="lead-cta" data-entity="board" data-lead_cta='1' data-sponsor="<?= $sponsorClientUrl ?>"></div>
                        <?php
                        if (!empty($translation_data)) {
                            ?>
                            <a href="<?= $url ?>" class="translateBtn">
                                <span class="webpSpriteIcon translateIcon1"></span>
                                <?= $msg ?>
                            </a>
                            <?php
                        }
                        ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>

    </header>
</div>

<!-- board menu -->
<?php if (!empty($pages)): ?>
    <nav class="stickyNavCls">
        <div class="pageRedirectionLinks">
            <p class="btn_left over">
                <i class="spriteIcon left_angle"></i>
            </p>
            <?php if (count($subMenu) > 8 && !$isMobile): ?>
                <p class="btn_right">
                    <i class="spriteIcon right_angle"></i>
                </p>
            <?php endif; ?>
            <?php if (count($subMenu) > 1 && $isMobile): ?>
                <p class="btn_right">
                    <i class="spriteIcon right_angle"></i>
                </p>
            <?php endif; ?>
            <ul>
                <?php foreach ($menuLinks as $key => $value):
                    $dataAttribute = str_replace(' ', '-', $value); ?>
                    <?php if (in_array($key, $subMenu)): ?>
                        <?php if (array_key_exists($value, $dropdowns)) { ?>
                            <li class="subNavDropDown mobileSubNavDropDown">
                        <?php } else { ?>
                            <li>
                        <?php } ?>
                            <?php if (($key == $pageName && empty($dropDownType)) || (!empty($dropDownType) && $key == $dropDownType)): ?>
                                <a <?php
                                if (array_key_exists($value, $dropdowns)) {
                                    foreach ($dropdowns[$value] as $subValue) {
                                        if ($subValue['page_slug'] == $pageName) {
                                            ?> href="<?= $key == 'overview' ? Url::toBoardDetail($boardSlug) : Url::toBoardDetail($boardSlug, DataHelper::getLangCode($board->lang_code), $key) ?>" <?php
                                        }
                                    }
                                } ?> class="activeLink" id="activeLinkScroll" title="<?= $board->display_name . ' ' . $value ?>"><?= Yii::t('app', $value) ?></a>
                            <?php else: ?>
                                <a class="<?= ($key == $pageName ? 'activeLink' : '') ?>" title="<?= $board->display_name . ' ' . $value ?>" href="<?= $key == 'overview' ? Url::toBoardDetail($boardSlug, DataHelper::getLangCode($board->lang_code)) : Url::toBoardDetail($boardSlug, DataHelper::getLangCode($board->lang_code), $key) ?>">
                                    <?= Yii::t('app', $value) ?>
                                </a>
                            <?php endif; ?>
                            <?php if (array_key_exists($value, $dropdowns)) { ?>
                                <span class="spriteIcon caret" data-list="<?= $dataAttribute ?>"></span>
                                <ul class="subNavDropDownMenu desktopOnly">
                                    <?php foreach ($dropdowns[$value] as $dropValue) {
                                        $title = ($value == 'Supplementary') ? $board->display_name . ' ' . $value . ' ' . $dropValue['page'] : $board->display_name . ' ' . $dropValue['page'] . ' ' . $value; ?>
                                        <li><a class="<?= (($dropValue['page_slug'] == $pageName) && ($key == $dropDownType)) ? 'subNavActive' : '' ?>" title="<?= $title ?>" href="<?= Url::toBoardDetailSubPage($boardSlug, $key, $dropValue['page_slug'], DataHelper::getLangCode($board->lang_code)) ?>"><?= $dropValue['page'] ?></a></li>
                                    <?php } ?>

                                </ul>
                            <?php } ?>

                            </li>
                            <?php if (array_key_exists($value, $dropdowns)) { ?>
                                <div class="mobileSubNavDropDownMenu" id=<?= $dataAttribute ?>>
                                    <div class="mobileSubNavDropDownDiv">
                                        <ul>
                                            <?php foreach ($dropdowns[$value] as $dropValue) {
                                                $title = ($value == 'Supplementary') ? $board->display_name . ' ' . $value . ' ' . $dropValue['page'] : $board->display_name . ' ' . $dropValue['page'] . ' ' . $value; ?>
                                                <li><a class="<?= (($dropValue['page_slug'] == $pageName) && ($key == $dropDownType)) ? 'subNavActive' : '' ?>" title="<?= $title ?>" href="<?= Url::toBoardDetailSubPage($boardSlug, $key, $dropValue['page_slug'], DataHelper::getLangCode($board->lang_code)) ?>"><?= $dropValue['page'] ?></a></li>
                                            <?php } ?>
                                        </ul>
                                    </div>
                                </div>
                            <?php } ?>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>
        </div>
    </nav>
<?php endif; ?>

<?php if ($isMobile): ?>
    <!--div class="lead-cta" data-entity="board" data-lead_cta='2' style="width: 390px; max-width: 390px; height: 56px; display: inline-block;"></div-->
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <?php if (!$isMobile && Url::toDomain() !=  Url::toBridgeU()): ?>
            <aside>
                <div class="horizontalRectangle">
                    <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                        <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__728x90')
                        ?>

                    </div>
                </div>
            </aside>
        <?php endif; ?>
        <?php if (!empty($pageContent->short_description)): ?>
            <div class="articelNote">
                <p><?= $pageContent->short_description ?></p>
            </div>
        <?php endif; ?>
        <div class="pageData">
            <?php if (!empty($author->slug)): ?>
                <div class="updated-info row">
                    <?php /*<div class="updatedBy">
                        <img data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= !empty($author) ? $author->name : '' ?>">
                    </div>*/ ?>
                    <div class="authorAndDate">
                        <a class="authorName" href="<?= Url::toAllAuthorPost($author->slug) ?>" title="<?= $author->name ?>"><?= !empty($author) ? $author->name : '' ?> <span class="spriteIcon verifiedBlueTickIcon"></span></a>
                        <p>Updated on - <?= Yii::$app->formatter->asDate($pageContent->updated_at ?? 'today') ?></p>
                    </div>
                </div>
            <?php else: ?>
                <div class="updated-info row">
                    <?php /*<div class="updatedBy">
                        <img data-src="<?= $authorImage ?>" src="<?= $authorImage ?>" alt="<?= !empty($author) ? $author->name : '' ?>">
                    </div>*/ ?>
                    <div class="authorAndDate">
                        <p class="authorName" title="Getmyuni Content Team">Getmyuni Content Team <span class="spriteIcon verifiedBlueTickIcon"></span></p>
                        <p>Updated on - <?= Yii::$app->formatter->asDate($pageContent->updated_at ?? 'today') ?></p>
                    </div>
                </div>

            <?php endif; ?>

            <?php if (!empty($recentActivity) && Url::toDomain() !=  Url::toBridgeU()): ?>
                <?= $this->render('../partials/_recentActivity', [
                    'recentActivity' => $recentActivity,
                    'title' => (!empty($board->display_name) ? $board->display_name : $board->name) . ' About'
                ]) ?>
            <?php endif;
            // $createdDate = new DateTime($pageContent->created_at);
            // $comparisonDate = new DateTime('2024-09-03 12:00:00');
            // if (isset($h2Text['h2']) && !empty($h2Text['h2']) && $createdDate > $comparisonDate):
            if (isset($h2Text['h2']) && !empty($h2Text['h2'])):
                ?>
                <?= $this->render('partials/_table-of-content', [
                    'h2Content' => $h2Text['h2'],
                ]); ?>
            <?php endif; ?>
            <?= ContentHelper::removeStyleTag(stripslashes(html_entity_decode(
                DataHelper::parseDomainUrlInContent(BoardHelper::parseBoardUrl($content, $board->slug))
            ))) ?>
        </div>
        <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
            <aside>
                <div class="horizontalRectangle mobileOnly">
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php if ($isMobile): ?>
                            <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__320x100')
                            ?>
                            <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x250')
                            ?>
                        <?php endif; ?>
                    </div>
                </div>
            </aside>
        <?php endif; ?>
        <!-- faqs -->
        <?php if (!empty($faqs)): ?>
            <?= $this->render('partials/_faq-card', [
                'faqs' => $faqs,
                'displayName' => $displayName,
                'pageName' => ($pageName == 'overview') ? ''  : $faqPageName
            ]) ?>
        <?php endif; ?>

        <!-- QNA card -->
        <?php if (!empty($forumQna)): ?>
            <section class="pageData">
                <?= $this->render('../qna/_qna-card', [
                    'qna' => $forumQna,
                    'pageName' => $pageName,
                    'entity' => Board::ENTITY_BOARD,
                    'entity_slug' => $boardSlug,
                    'page_name' => $displayName
                ]) ?>
            </section>
        <?php endif; ?>

        <?php /*if (!empty($subject)): ?>
            <?php if (($pageContent->page_slug == 'sample-papers' || $pageContent->page_slug == 'previous-year-question-papers' || $pageContent->page_slug == 'solved-question-papers') && !empty($subject)): ?>
                <div class="pageData">
                    <?php foreach ($subject as $key => $value): ?>
                        <h3><?= $key ?></h3>
                        <?php foreach ((array_unique($value, SORT_REGULAR)) as $k => $v): ?>
                            <a class="btn" href="<?= Url::toBoardSamplePaperDetail($v['pageSlug']) ?>"><?= ucwords(str_replace('-', ' ', $v['subject_slug'])) ?></a>
                        <?php endforeach; ?>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        <?php endif;*/ ?>
        <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
            <aside>
                <div class="horizontalRectangle desktopOnly">
                    <div class="appendAdDiv" style="background:#EAEAEA; margin-bottom : 30px;">
                        <?php if (!$isMobile): ?>
                            <?php echo Freestartads::unit('getmyuni-com_incontent_IAI_728x250', '__728x90 __336x280') ?>
                        <?php endif; ?>
                    </div>
                </div>
            </aside>
        <?php endif; ?>
        <!-- college widget -->
        <?php if (!empty($stateColleges)): ?>
            <?= $this->render('partials/_colleges-card', [
                'stateColleges' => $stateColleges,
                'board' => $board,
                'assetUrl' => $assetUrl
            ]) ?>
        <?php endif; ?>

        <!-- exam widget -->
        <?php if (!empty($exams)): ?>
            <?= $this->render('partials/_exam-card', [
                'exams' => $exams,
                'board' => $board,
                'assetUrl' => $assetUrl
            ]) ?>
        <?php endif; ?>

        <div class="removeFixedQuickLink">
            <!-- Do not Delete this -->
        </div>
    </div>

    <div class="col-md-4">
        <aside>
            <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                <div class="getSupport">
                    <!--div class="row">
                        <img src="<!-?= '/yas/images/bulbIcon.svg' ?>" width="80" height="80" alt="">
                        <p>Are you Interested in this Board?</p>
                    </div--->
                    <!-- <p class="getSupport__subheading">Are you Interested in this Board?</p> -->
                    <div class="button__row__container">
                        <div class="lead-cta" data-entity="board" data-lead_cta='3' data-sponsor="<?= $sponsorClientUrl ?>"></div>
                    </div>
                </div>
            <?php endif; ?>
            <aside class="board-aside">
                <div class="quickLinks quickLinks-cls">
                    <h2><?= Yii::t('app', 'Quick Read') ?></h2>
                    <ul>
                        <?php foreach ($menuLinks as $key => $value):
                            if (in_array($key, $subMenu)) {
                                $title = in_array($key, $excludedSubpage) ? $board->display_name . ' ' . ($key == 'overview' ? '' : $value) : $board->display_name . ' ' . ($key == 'overview' ? '' : $value) . ' ' . $year;
                            } else {
                                continue;
                            } ?>
                            <li>
                                <?php if ($key == $pageName): ?>
                                    <?php continue; ?>
                                <?php endif; ?>
                                <a title="<?= $title; ?>" href="<?= $key == 'overview' ? Url::toBoardDetail($board->slug, DataHelper::getLangCode($board->lang_code)) : Url::toBoardDetail($board->slug, DataHelper::getLangCode($board->lang_code), $key) ?>">
                                    <?= $title; ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </aside>
            <?php if (!empty($board) && isset($board->level) && $board->level == '12'): ?>
                <?= $this->render('/board/partials/_board-inlinks', [
                    'board' => $board,
                    'state' => $state ?? null,
                    'streamColleges' => $streamColleges,
                ]); ?>
            <?php endif; ?>
            <?php /*if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>

                <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                    <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                        'trendings' => $trendingArticles,
                        'recentArticles' => $recentArticles,
                    ]); ?>
                <?php endif; */ ?>

            <?php /* not needed for now
            <div class="sideBarSection desktopOnly">
                <p class="sidebarHeading"><span class="spriteIcon alarmIcon"></span> NOTIFICATIONS</p>
                <div class="sidebarLinks">
                    <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                        <div class="sidebarTextLink">
                            <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                            <p class="subText">Posted on 07-11-2020</p>
                        </div>
                    </a>
                    <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                        <div class="sidebarTextLink">
                            <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                            <p class="subText">Posted on 07-11-2020</p>
                        </div>
                    </a>
                    <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                        <div class="sidebarTextLink">
                            <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                            <p class="subText">Posted on 07-11-2020</p>
                        </div>
                    </a>
                    <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                        <div class="sidebarTextLink">
                            <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                            <p class="subText">Posted on 07-11-2020</p>
                        </div>
                    </a>
                    <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                        <div class="sidebarTextLink">
                            <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                            <p class="subText">Posted on 07-11-2020</p>
                        </div>
                    </a>
                </div>
            </div>
            <?php */ ?>

        </aside>

        <?php /* not needed for now
        <div class="sideBarSection mobileOnly">
            <p class="sidebarHeading"><span class="spriteIcon alarmIcon"></span> NOTIFICATIONS</p>
            <div class="sidebarLinks">
                <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                    <div class="sidebarTextLink">
                        <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                        <p class="subText">Posted on 07-11-2020</p>
                    </div>
                </a>
                <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                    <div class="sidebarTextLink">
                        <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                        <p class="subText">Posted on 07-11-2020</p>
                    </div>
                </a>
                <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                    <div class="sidebarTextLink">
                        <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                        <p class="subText">Posted on 07-11-2020</p>
                    </div>
                </a>
                <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                    <div class="sidebarTextLink">
                        <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                        <p class="subText">Posted on 07-11-2020</p>
                    </div>
                </a>
                <a href="javascript:;" title="Sathyabama Institute of technology" class="listCard">
                    <div class="sidebarTextLink">
                        <p class="cardText">JEE Main Marks vs Percentile 2021 </p>
                        <p class="subText">Posted on 07-11-2020</p>
                    </div>
                </a>
            </div>
        </div>
        <?php */ ?>

        <!-- <aside> -->
        <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
            <div class="sidebarAds">
                <div class="appendAdDiv" style="background:#EAEAEA;">
                    <?php if ($isMobile): ?>
                        <?php echo Freestartads::unit('getmyuni-com_siderail_right_2', '__200x600')
                        ?>
                    <?php else: ?>
                        <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x250')
                        ?>
                    <?php endif; ?>
                </div>
                <?php if (!$isMobile): ?>
                    <div class="appendAdDiv" style="background:#EAEAEA;">
                        <?php echo Freestartads::unit('getmyuni-com_siderail_right_2', '__300x250') ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        <!-- </aside> -->
    </div>
</div>

<!-- related article -->
<?php if (!empty($article)): ?>
    <?= $this->render('../partials/_productArticleCard', [
        'relatedArticles' => $article,
        'title' => 'Explore Articles on Board Exams',
    ]); ?>
<?php endif; ?>

<!-- related News -->
<?php if (!empty($news)): ?>
    <?= $this->render('../partials/_productNewsCard', [
        'news' => $news,
        'title' => 'Latest NEWS on Board Exams',
    ]); ?>
<?php endif; ?>

<!-- Board Article  -->
<?php /*if (!empty($articles)): ?>
    <?= $this->render('partials/_board-articles', [
        'articles' => $article,
        'board' => $board,
    ]) ?>
<?php endif; ?>
<!-- Board News  -->
<?php if (!empty($boardExams)): ?>
    <?= $this->render('partials/_board-news', [
        'boardExamNews' => $news,
        'board' => $board,
    ]) ?>
<?php endif; */ ?>

<!-- related article -->
<?php /*if (!empty($board->article)): ?>
    <?= $this->render('../partials/_productArticleCard', [
        'relatedArticles' => $board->article,
    ]); ?>
<?php endif; ?>

<!-- related News -->
<?php if (!empty($board->news)): ?>
    <?= $this->render('../partials/_productNewsCard', [
        'news' => $board->news,
    ]); ?>
<?php endif; */ ?>