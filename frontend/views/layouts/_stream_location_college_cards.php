<?php

use common\models\College;
use frontend\helpers\Url;
use yii\helpers\BaseStringHelper;

$colleges = $colleges ?? [];
$title = $title ?? 'Popular Colleges Near You';

?>

<?php if (!empty($colleges)): ?>
    <div class="carouselDiv">
        <h2><?= $title ?></h2>
        <div class="sliderslide four-cardDisplay">
            <?php if (count($colleges) > 2): ?>
                <i class="spriteIcon scrollLeft"></i>
                <i class="spriteIcon scrollRight"></i>
            <?php endif; ?>
            <div class="carouselSection">
                <?php foreach ($colleges as $college): ?>
                    <div class="cardUpperBody">
                        <img src="<?= !empty($college['cover_image']) ? Url::getCollegeBannerImage($college['cover_image']) : Url::toDefaultCollegeBanner() ?>" class="college-img" alt="">
                        <div class="college-section">
                            <div class="college-logo">
                                <img src="<?= !empty($college['logo_image']) ? Url::getCollegeLogo($college['logo_image']) : Url::defaultCollegeLogo() ?>" alt="">
                            </div>
                            <div class="college-name">
                                <a href="<?= Url::toCollege($college['slug']) ?>" title="<?= $college['name'] ?>"><?= BaseStringHelper::truncate($college['name'], 45) ?? $college['name'] . ',' ?></a>
                                <h3 class="collegesection"><span><?= $college['city_name'] ?></span>
                                    <?php if ($college['type']): ?>
                                        <span class="collegeType"><?= ucfirst($college['type']) ?></span>
                                    <?php endif; ?>
                                    <?php if ($college['nirf_rank']): ?>
                                        <span class="collegeType">NIRF #<?= $college['nirf_rank'] ?></span>
                                    <?php endif; ?>
                                </h3>
                            </div>

                            <?php if (!empty($college['categoryRating'])): ?>
                                <span class="collegeRating">
                                    <?= $college['categoryRating']; ?>
                                    <img src="https://static.collegedekho.com/static-up/images/examCard/star-rating.svg" />
                                </span>
                            <?php endif; ?>
                        </div>

                        <?php if ($college['shortlisted_count'] > 0): ?>
                            <div class="shortlist">
                                <img src="https://static.collegedekho.com/static-up/images/examCard/student.svg">
                                <p>Shortlisted by <b><?= number_format($college['shortlisted_count']) ?></b> students</p>
                            </div>
                        <?php endif; ?>

                        <?= frontend\helpers\Html::leadButton(
                            'Apply Now',
                            [
                                'entity' => College::ENTITY_COLLEGE,
                                'entityId' => $college['id'],
                                'ctaLocation' => 'popular_college_card_cta',
                                'ctaText' => 'Apply Now',
                                'leadformtitle' => 'Register Now to Apply',
                                'subheadingtext' => '',
                                'image' => !empty($college['logo_image']) ? Url::getCollegeLogo($college['logo_image']) : Url::defaultCollegeLogo(),
                            ],
                            ['class' => 'btnApply'],
                            'js-open-lead-form-new'
                        )
                        ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
<?php endif; ?>