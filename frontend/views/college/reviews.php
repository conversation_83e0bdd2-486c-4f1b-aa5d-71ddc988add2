<?php

use common\models\College;
use common\helpers\ContentHelper;
use common\helpers\CollegeHelper;
use common\models\LiveUpdate;
use common\services\CollegeService;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use yii\widgets\ListView;

// $reviews->prepare();
$defaultSeoInfo = ContentHelper::getCollegeDefaultSeoInfo(!empty($college->display_name) ? $college->display_name : $college->name, 'reviews');

if (Yii::$app->request->get('page') > 1) {
    $this->title = !empty($content->meta_title) ? CollegeHelper::parseContent($content->meta_title) . ' - Page ' . Yii::$app->request->get('page') : $defaultSeoInfo['title'] . ' Page ' . Yii::$app->request->get('page');
    $this->context->description = !empty($content->meta_description) ? CollegeHelper::parseContent($content->meta_description) . ' Page ' . Yii::$app->request->get('page') : $defaultSeoInfo['description'] . ' Page ' . Yii::$app->request->get('page');
} else {
    $this->title = !empty($content->meta_title) ? CollegeHelper::parseContent($content->meta_title) : $defaultSeoInfo['title'];
    $this->context->description = !empty($content->meta_description) ? CollegeHelper::parseContent($content->meta_description) : $defaultSeoInfo['description'];
}

$isMobile = \Yii::$app->devicedetect->isMobile();
$this->context->ogImage = !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner();

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$url = (new CollegeService)->checkFilterPageStatus([], !empty($city) ? $city->slug : '', '', '') == College::SPONSORED_NO ? '/all-colleges/' . (!empty($city) ? $city->slug : '') : '';
if (!empty($url) && !empty($city)) {
    $this->params['breadcrumbs'][] = ['label' => 'Colleges in ' . $city->name, 'url' => [$url], 'title' => 'Colleges in ' . $city->name];
}
$this->params['breadcrumbs'][] = ['label' => !empty($college->display_name) ? $college->display_name : $college->name, 'url' => [$college->slug], 'title' => !empty($college->display_name) ? $college->display_name : $college->name];

if (Yii::$app->request->get('page') > 1) {
    $this->params['breadcrumbs'][] = ['label' => !empty($college->display_name) ? $college->display_name . ' Reviews' : $college->name . ' Reviews', 'url' => [Url::toCollege($college->slug, 'reviews')]];
    $this->params['breadcrumbs'][] = 'Page ' . Yii::$app->request->get('page');
} else {
    $this->params['breadcrumbs'][] = 'Reviews';
}

$this->registerLinkTag(['href' => !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner(), 'rel' => 'preload', 'as' => 'image',  'fetchpriority' => 'high']);
$this->registerLinkTag(['href' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(), 'rel' => 'preload', 'as' => 'image',  'fetchpriority' => 'high']);
// page specific assets
$this->registerCssFile(Yii::$app->params['cssPath'] . 'college-new.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');


//gmu params
$this->params['entity'] = College::ENTITY_COLLEGE;
$this->params['course_count'] = !empty($courseCount) ? $courseCount : '';
$this->params['entity_id'] = $college->id ?? 0;
$this->params['interested_location'] = $city->id ?? null;
$this->params['entity_name'] = $college->name ?? '';
$this->params['entitySlug'] = $college->slug ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $college->display_name ?? '';
$this->params['pageName'] = 'reviews';

// schema
$this->params['schema'] = CollegeHelper::collegeSchema($college, $revCategoryRating, $reviewCount);
?>

<div class="collegeReviewPage">
    <?= $this->render('partials/_header-new', [
        'menus' => $menus ?? [],
        'college' => $college,
        'content' => $content,
        'pageName' => 'reviews',
        'rating' => $revCategoryRating ? CollegeHelper::getTotalRating($revCategoryRating) : '',
        'type' => $type,
        'approval' => $approval,
        'brochure' => $brochure,
        'heading' => !empty($content->h1) ? CollegeHelper::parseContent($content->h1) : (empty($defaultSeoInfo['h1']) ? $defaultSeoInfo['title'] : $defaultSeoInfo['h1']),
        'categoryRating' => $revCategoryRating,
        'city' => $city,
        'state' => $state,
        'reviewCount' => $reviewCount,
        'author' => $authorDetail,
        'profile' => $profile,
        'dynamicCta' => $dynamicCta,
        'sponsorClientUrl' => !empty($sponsorClientUrl->redirection_link)  ? $sponsorClientUrl->redirection_link : '',
        'review' => [],
    ]) ?>

    <?= $this->render('partials/_menu-card', [
        'menus' => $menus,
        'college' => $college,
        'pageName' => 'reviews',
        'dropdown' => $dropdowns
    ]) ?>

    <div class="row">
        <div class="col-md-8">
            <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU() && !$isMobile): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                            <?php /* if ($isMobile): ?>
                                <?php echo Ad::unit('GMU_COLLEGE_REVIEWS_WAP_300x100_ATF', '[300,100]') ?>
                            <?php else: */ ?>
                            <?php echo Ad::unit('GMU_COLLEGE_REVIEWS_WEB_728x90_ATF', '[728,90]') ?>
                            <?php //endif;
                            ?>
                        </div>
                    </div>
                </aside>
            <?php endif; ?>
            <div class="reviewsSection" id="loadReview">
                <h2 class=""><?= !empty($college->display_name) ? $college->display_name : $college->name ?> Reviews
                    <!-- <span class="spriteIcon tooltipIcon"><span class="tooltipIconText">Lorem, ipsum dolor sit amet
                            consectetur adipisicing elit. Deserunt, dignissimos ipsa </span></span> -->
                </h2>

                <?= $this->render('partials/_rating-card', [
                    'distrbutionRating' => $revDistributionRating,
                    'categoryRating' => $revCategoryRating,
                    'collegeId' => $college->id,
                    'reviewCount' => $reviewCount ?? '',
                ]) ?>

                <?php $this->render('partials/_review-filter', []) ?>

                <?php if (!empty($reviews)): ?>
                    <?= $this->render('partials/_review-sub-card', [
                        'reviews' => $reviews,
                        'category' => 5
                    ]) ?>

                    <div id="paginationData"></div>
                <?php endif; /*?>
                <div class="moreReviews">
                <?php
                    echo ListView::widget([
                        'dataProvider' => $reviews,
                        'itemView' => 'partials/_review-list-card',
                        'viewParams' => [
                            'fullView' => true,
                            'context' => 'main-page',
                            'isMobile' => $isMobile,
                            'categoryRating' => $revCategoryRating,
                        ],
                        //layout' => "{items}\n{pager}",

                        // 'pager' => [
                        //     'prevPageCssClass' => '',
                        //     'maxButtonCount' => 5,
                        // ]
                    ]);
                    ?>
                </div><?php */ ?>
                <?php if (count($reviews) > 1 && $reviewCount > count($reviews)): ?>
                    <button id="loadMoreReview" class="primaryBtn viewAllReviewsBtn" data-college='<?= $college->id ?>' data-page="1"> Load More Reviews</button>
                <?php endif; ?>    
            </div>
            <div class="popular-colleges-widget" data-college-id="<?= $college->id ?>" data-entity="college" data-state-id="<?= $college->city->state_id ?>"></div>

            <?php if ($liveApplicationForm && $isMobile && $college->is_sponsored == College::SPONSORED_NO && Url::toDomain() !=  Url::toBridgeU()): ?>
                <div id="liveApplicationForm"></div>
            <?php endif; ?>

            <?php if (!empty($forums)): ?>
                <?= $this->render('partials/_forum-card', [
                    'forums' => $forums,
                    'viewAllUrl' => $college->slug
                ]) ?>
            <?php endif; ?>
        </div>

        <div class="col-md-4 pl-0">
            <aside>
                <?php if (Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div class="getSupport">
                        <!-- <div class="row">
                            <img class="lazyload" loading="lazy" width="80" height="80" data-src="/yas/images/bulbIcon.svg" src="/yas/images/bulbIcon.svg" alt="">
                            <p>Are You Interested In This College?</p>
                        </div> -->
                        <div class="lead-cta lead-cta-cls-button" data-lead_cta="0" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>"></div>
                    </div>
                <?php endif; ?>
                <?php /*if (!empty($featuredNews) || !empty($recentNews)): ?>
                    <?= $this->render('@frontend/views/news/partials/_sidebar-tab-news', [
                        'featured' => $featuredNews,
                        'recents' => $recentNews,
                        'isAmp'  => 0,
                        'liveTagID' => LiveUpdate::LIVE_NEWS_TAG_ID,
                        'smallIcone' => 1
                    ]); ?>
                <?php endif; ?>

                <?php if (!empty($featuredArticles) || !empty($recentArticles)): ?>
                    <?= $this->render('@frontend/views/article/partials/_sidebar-articles', [
                        'trendings' => $trendingArticles,
                        'recentArticles' => $recentArticles,
                    ]); ?>
                <?php endif;*/ ?>
            </aside>
            <?php if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
                <?= $this->render('partials/_sidebar-ads.php', [
                    'class' => 'desktopOnly',
                    'ads' => [
                        ['slot' => 'GMU_COLLEGE_REVIEWS_WEB_300x250_MTF_1', 'size' => '[300, 250]', 'isMobile' => false],
                        ['slot' => 'GMU_COLLEGE_REVIEWS_WEB_300x250_MTF_2', 'size' => '[300, 250]', 'isMobile' => false]
                    ]
                ]) ?>
            <?php endif; ?>
        </div>
        
        <div class="col-md-12">
            <!-- other colleges under university -->
            <?php if (!empty($affiliatedCollege) && !empty($parentCollege)): ?>
                <?= $this->render('partials/_college-card', [
                    'title' => 'Other Colleges under ' . (!empty($parentCollege['display_name']) ? $parentCollege['display_name'] : $parentCollege['name']),
                    'colleges' => $affiliatedCollege,
                    'city' => $city,
                    'state' => $state,
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($collegeByDiscipline) && !empty($collegeByDiscipline['colleges']) && count($collegeByDiscipline['colleges']) > 2): ?>
                <?= $this->render('partials/_similar-college-card', [
                    'collegeByDiscipline' => $collegeByDiscipline['colleges'],
                    'sponsorStatus' => $collegeByDiscipline['sponsorStatus'],
                    'college' => $college
                ]) ?>
            <?php endif; ?>

            <?php if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
                <?= $this->render('partials/_college-card', [
                    'title' => 'Explore Nearby Colleges',
                    'colleges' => $nearByCollege,
                    'viewAllUrl' => !empty($city) ? "{$city->slug}" : '',
                    'city' => $city,
                    'state' => $state,
                ]) ?>
            <?php endif; ?>

            <!-- Popular Colleges -->
            <?php if (!empty($popularCollege)): ?>
                <?= $this->render('partials/_college-card', [
                    'title' => 'Popular Colleges',
                    'colleges' => $popularCollege,
                    'city' => $city,
                    'state' => $state,
                ])
                ?>
            <?php endif; ?>

            <!-- related article -->
            <?php if (!empty($article)): ?>
                <?= $this->render('../partials/_productArticleCard', [
                    'relatedArticles' => $article,
                ]); ?>
            <?php endif; ?>

            <!-- related News -->
            <?php if (!empty($news)): ?>
                <?= $this->render('../partials/_productNewsCard', [
                    'news' => $news,
                ]); ?>
            <?php endif;

            if ($college->is_google_ads == College::ADS_ACTIVE && Url::toDomain() !=  Url::toBridgeU()): ?>
                <aside>
                    <div class="horizontalRectangle">
                        <div class="appendAdDiv" style="background:#EAEAEA;">
                            <?php if ($isMobile): ?>
                                <?php echo Freestartads::unit('getmyuni-com_bottom', '__300x250') ?>
                            <?php else: ?>
                                <?php echo Freestartads::unit('getmyuni-com_bottom', '__728x90') ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </aside>
            <?php endif; ?>
            <div class="removeFixedQuickLink">
                <!-- Do not Delete this -->
            </div>
        </div>
    </div>
</div>
<?php /*if (!empty($sponsorClientUrl->redirection_link)):?>
    <?= $this->render('partials/_apply-now', [
        'sponsorClientUrl' => $sponsorClientUrl,
        'college' => $college
    ]) ?>
<?php  endif;*/ ?>