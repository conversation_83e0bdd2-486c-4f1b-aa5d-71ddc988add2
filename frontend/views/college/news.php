<?php

use common\models\College;
use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\LiveUpdate;
use common\services\CollegeService;
use frontend\assets\AppAsset;
use frontend\helpers\Url;
use yii\helpers\Html;
use yii\helpers\Inflector;
use yii\widgets\ListView;

$defaultSeoInfo = ContentHelper::getCollegeDefaultSeoInfo(!empty($college->display_name) ? $college->display_name : $college->name, 'news');
$defaultTitle = $college->display_name . ' Articles & News ' . date('Y');
$this->title = !empty($content->meta_title) ? CollegeHelper::parseContent($content->meta_title) : $defaultSeoInfo['title'];
$this->context->description = !empty($content->meta_description) ? CollegeHelper::parseContent($content->meta_description) : $defaultSeoInfo['description'];
$isMobile = \Yii::$app->devicedetect->isMobile();
$this->context->ogImage = !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner();

// breadcrumb
$this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
$url = (new CollegeService)->checkFilterPageStatus([], !empty($city) ? $city->slug : '', '', '') == College::SPONSORED_NO ? '/all-colleges/' . (!empty($city) ? $city->slug : '') : '';
if (!empty($url) && !empty($city)) {
    $this->params['breadcrumbs'][] = ['label' => 'Colleges in ' . $city->name, 'url' => [$url], 'title' => 'Colleges in ' . $city->name];
}
$this->params['breadcrumbs'][] = ['label' => !empty($college->display_name) ? $college->display_name : $college->name, 'url' => [$college->slug], 'title' => !empty($college->display_name) ? $college->display_name : $college->name];

if (Yii::$app->request->get('page') > 1) {
    $this->params['breadcrumbs'][] = ['label' => !empty($college->display_name) ? $college->display_name . ' Articles & News' : $college->name . ' Articles & News', 'url' => [Url::toCollege($college->slug, 'news')]];
    $this->params['breadcrumbs'][] = 'Page ' . Yii::$app->request->get('page');
} else {
    $this->params['breadcrumbs'][] = 'Articles & News';
}

$this->registerLinkTag(['href' => !empty($college->cover_image) ? Url::getCollegeBannerImage($college->cover_image) : Url::toDefaultCollegeBanner(), 'rel' => 'preload', 'as' => 'image']);
$this->registerLinkTag(['href' => !empty($college->logo_image) ? Url::getCollegeLogo($college->logo_image) : Url::defaultCollegeLogo(), 'rel' => 'preload', 'as' => 'image']);
// page specific assets
// $this->registerCssFile('/yas/css/version2/side_bar.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'college-new.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'side_bar.css', [
    'depends' => [AppAsset::class],
    'media' => 'print',
    'onload' => 'this.media="all"'
], 'sidebar-css-theme');
// $this->registerCssFile(Yii::$app->params['cssPath'] . 'article_landing.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'author.css', ['depends' => [AppAsset::class]]);


//gmu params
$this->params['entity'] = College::ENTITY_COLLEGE;
$this->params['course_count'] = !empty($courseCount) ? $courseCount : '';
$this->params['entity_id'] = $college->id ?? 0;
$this->params['interested_location'] = $city->id ?? null;
$this->params['entity_name'] = $college->name ?? '';
$this->params['entitySlug'] = $college->slug ?? '';
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$this->params['entityDisplayName'] = $college->display_name ?? '';
$this->params['pageName'] = 'news';

// schema
$this->params['schema'] = CollegeHelper::collegeSchema($college, $revCategoryRating, $reviewCount);
?>

<div class="subPage">
    <!-- header -->
    <?= $this->render('partials/_header-new', [
        'menus' => $menus ?? [],
        'college' => $college,
        'content' => [],
        'pageName' => 'news',
        'rating' => $revCategoryRating ? CollegeHelper::getTotalRating($revCategoryRating) : '',
        'type' => $type,
        'approval' => $approval,
        'brochure' => $brochure,
        'heading' => $defaultSeoInfo['h1'],
        'defaultTitle' => $defaultTitle,
        'categoryRating' => $revCategoryRating,
        'city' => $city,
        'state' => $state,
        'reviewCount' => $reviewCount,
        'author' => $authorDetail,
        'profile' => $profile,
        'dynamicCta' => $dynamicCta,
        'sponsorClientUrl' => !empty($sponsorClientUrl->redirection_link)  ? $sponsorClientUrl->redirection_link : '',
    ]) ?>

    <!-- page specific navigation -->
    <?= $this->render('partials/_menu-card', [
        'menus' => $menus,
        'college' => $college,
        'pageName' => 'news',
        'dropdown' => $dropdowns
    ]) ?>

    <div class="row">
        <div class="col-md-8">
        <?php if (!empty($collegeNotificationUpdate)): ?>
        <div class="pageData <?=  count($collegeNotificationUpdate) >5  ? 'pageInfo' : '' ?>">
        <div class="infoPage">
            <?= $this->render('../partials/_collegeNotificationUpdate', [
                 'collegeNotificationUpdate' => $collegeNotificationUpdate,
                 'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Articles & News',
                 'isShowTitle'=>true
        ]) ?>
        </div> 
        </div>
        <?php endif;?>
            <?php
            if (!empty($content->content)): ?>
                    <?= $this->render('partials/_main-content-card', [
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Articles & News',
                    'content' => CollegeHelper::parseContent(ContentHelper::htmlDecode($content->content)),
                    'contentAuthor' =>  [],
                    'author' => $authorDetail,
                    'profile' => $profile,
                    'removeShowMore' => true,
                    'recentActivity' => $recentActivity
                ]) ?>
            <?php endif;

            if ($liveApplicationForm && $isMobile && $college->is_sponsored == College::SPONSORED_NO && Url::toDomain() !=  Url::toBridgeU()): ?>
                <div id="liveApplicationForm"></div>
            <?php endif; ?>

            <?php if (!empty($collegeArticlesNews['categories'])): ?>
                <div class="browseArticleSection collegeNewsArticles" data-college_slug="<?=$college->slug?>">
                    <div class="articleTypes">
                        <ul>
                            <?php $catCount = 0; ?>
                            <?php foreach ($collegeArticlesNews['categories'] as $category => $value): ?>
                                <li><a href="javascript:;" data-tab="<?= $value ?>" class="<?= $catCount == 0 ? 'activeLink activeArticles' : '' ?> scrollClick articleNewsClick <?= $value ?>scroll " data-scroll="true" data-offset="6"><?= Yii::t('app', Inflector::titleize($value)) ?></a></li>
                                <?php $catCount++ ?>
                            <?php endforeach; ?>
                        </ul>
                    </div>

                    <div class="articlesDisplay">
                        <?php $listCount = 0 ?>
                        <?php foreach ($collegeArticlesNews['categories'] as $cat => $val): ?>
                            <?php if ($val == 'articles' || $val == 'news'): ?>
                                <?php if ($val == 'articles'): ?>
                                    <?php $data = $collegeArticlesNews['articles'];
                                    $file = 'partials/_collegeArticlesCard'; ?>
                                <?php elseif ($val == 'news'): ?>
                                    <?php $data = $collegeArticlesNews['news'];
                                    $file = 'partials/_collegeNewsCard'; ?>
                                <?php endif; ?>
                                <div id="<?= $val ?>" data-list-id="w<?php echo $listCount; ?>" class="tab-content <?= $listCount == 0 ? 'activeLink' : '' ?>">
                                    <div class="row">
                                        <?=$this->render($file, ['models' => $data, 'count' => $val == 'articles' ? $collegeArticlesNews['totalArticlesCount'] : $collegeArticlesNews['totalNewsCount']])?>
                                        <div class="paginationDataArticlesNews row"></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <?php $listCount++ ?>
                            <?php if ($val == 'articles'): ?>
                            <button id="collegeArticlesBtn" class="primaryBtn viewAllArticlesNewsBtn loadMoreArticlesNews" style="display: <?=$collegeArticlesNews['totalArticlesCount'] > 12 ? 'block' : 'none'?>" data-tab="articles"> Show More </button>
                            <?php elseif ($val == 'news'): ?>
                            <button id="collegeNewsBtn" class="primaryBtn viewAllArticlesNewsBtn loadMoreArticlesNews" style="display: <?=($collegeArticlesNews['totalNewsCount'] > 12 ? (empty($collegeArticlesNews['articles']) ? 'block' : 'none'): 'none')?>" data-tab="news"> Show More </button>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <div class="removeFixedQuickLink">
                <!-- Do not Delete this -->
            </div>
            <!-- reviews widget -->
            <?php if (!empty($reviews)): ?>
                <?= $this->render('partials/_review-card', [
                    'title' => (!empty($college->display_name) ? $college->display_name : $college->name) . ' Reviews',
                    'reviews' => $reviews,
                    'distrbutionRating' => $revDistributionRating,
                    'categoryRating' => $revCategoryRating,
                    'collegeId' => $college->id,
                    'viewAllURL' => !empty($menus) && !is_numeric($menus['reviews']) ? Url::toCollege($college->slug, 'reviews') : '',
                    'reviewCount' => $reviewCount ?? '',
                    'category' => 6
                ]) ?>
            <?php endif; ?>
             <div class="popular-colleges-widget" data-college-id="<?= $college->id ?>" data-entity="college" data-state-id="<?= $college->city->state_id ?>"></div>
        </div>

        <div class="col-md-4 noSticky">
            <aside>
                <?php if (Url::toDomain() !=  Url::toBridgeU()):?>
                    <div class="getSupport">
                        <!-- <div class="row">
                            <img class="lazyload" loading="lazy" width="80" height="80" data-src="/yas/images/bulbIcon.svg" src="/yas/images/bulbIcon.svg" alt="">
                            <p>Are You Interested In This College?</p>
                        </div> -->
                        <div class="lead-cta lead-cta-cls-button" data-lead_cta="0" data-image="<?= $college->logo_image ?>" data-entity="college" data-sponsor="<?= $sponsorClientUrl->redirection_link ?? '' ?>"></div>
                    </div>
                <?php endif; ?>
                <?php if (!empty($college->parent_id)): ?>
                    <?= $this->render('partials/_main-campus', [
                        'college' => $parentCollege,
                    ]) ?>
                <?php endif; ?>

                <?php if ($liveApplicationForm && !$isMobile && $college->is_sponsored == College::SPONSORED_NO && Url::toDomain() !=  Url::toBridgeU()): ?>
                    <div id="liveApplicationForm"></div>
                <?php endif; ?>
            </aside>
        </div>
    </div>

    <?php if (!empty($forums)): ?>
        <?= $this->render('partials/_forum-card', [
            'forums' => $forums,
            'viewAllUrl' => $college->slug
        ]) ?>
    <?php endif; ?>

    <?php if (!empty($collegeByDiscipline) && !empty($collegeByDiscipline['colleges']) && count($collegeByDiscipline['colleges']) > 2): ?>
        <?= $this->render('partials/_similar-college-card', [
            'collegeByDiscipline' => $collegeByDiscipline['colleges'],
            'sponsorStatus' => $collegeByDiscipline['sponsorStatus'],
            'college' => $college
        ]) ?>
    <?php endif; ?>

    <!-- other colleges under university -->
    <?php if (!empty($affiliatedCollege) && !empty($parentCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Other Colleges under ' . (!empty($parentCollege['display_name']) ? $parentCollege['display_name'] : $parentCollege['name']),
            'colleges' => $affiliatedCollege,
            'city' => $city,
            'state' => $state,
        ]) ?>
    <?php endif; ?>

    <!-- Nearby colleges -->
    <?php if (!empty($nearByCollege) && count($nearByCollege) > 2): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Explore Nearby Colleges',
            'colleges' => $nearByCollege,
            'viewAllUrl' => !empty($city) ? "{$city->slug}" : '',
            'city' => $city,
            'state' => $state,
        ]) ?>
    <?php endif; ?>

    <!-- Popular Colleges -->
    <?php if (!empty($popularCollege)): ?>
        <?= $this->render('partials/_college-card', [
            'title' => 'Popular Colleges',
            'colleges' => $popularCollege,
            'city' => $city,
            'state' => $state,
        ])
        ?>
    <?php endif; ?>

    <!-- related article -->
    <?php if (!empty($article)): ?>
        <?= $this->render('../partials/_productArticleCard', [
            'relatedArticles' => $article,
        ]); ?>
    <?php endif; ?>

    <!-- related News -->
    <?php if (!empty($news)): ?>
        <?= $this->render('../partials/_productNewsCard', [
            'news' => $news,
        ]); ?>
    <?php endif; ?>
</div>