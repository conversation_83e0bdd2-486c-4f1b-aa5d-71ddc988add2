<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\CutOff;

/**
 * CutOffSearch represents the model behind the search form of `common\models\CutOff`.
 */
class CutOffSearch extends CutOff
{

    public $college;
    public $exam;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'category', 'gender', 'type', 'year', 'round', 'opening_rank', 'closing_rank', 'percentile', 'closing_score'], 'integer'],
            [['exam_id', 'course_id', 'specialization_id', 'college_id'], 'string'],
            [['college', 'exam', 'course', 'specialization', 'created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = CutOff::find();
        $query->joinWith(['college', 'course', 'exam', 'specialization']);
        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            // 'id' => $this->id,
            // 'college_id' => $this->college_id,
            // 'exam_id' => $this->exam_id,
            // 'course_id' => $this->course_id,
            // 'specialization_id' => $this->specialization_id,
            'category' => $this->category,
            'gender' => $this->gender,
            'type' => $this->type,
            'year' => $this->year,
            'round' => $this->round,
            'opening_rank' => $this->opening_rank,
            'closing_rank' => $this->closing_rank,
            'percentile' => $this->percentile,
            'closing_score' => $this->closing_score,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere([
            'college.id' => $this->college_id,
            'exam.id' => $this->exam_id,
        ]);

        return $dataProvider;
    }


    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function home($params)
    {
        $query = CutOff::find()->where(['college_id' => $params['id']])->groupBy('exam_id', 'year');
        $query->joinWith(['college', 'course', 'exam', 'specialization']);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere(['like', 'exam.name', $this->exam_id])
            ->andFilterWhere(['like', 'college.name', $this->college_id])
            ->andFilterWhere(['like', 'year', $this->year]);

        return $dataProvider;
    }
}
