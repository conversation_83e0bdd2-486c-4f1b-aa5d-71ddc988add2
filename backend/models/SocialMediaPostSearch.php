<?php

namespace backend\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\SocialMediaPost;

/**
 * SocailMediaPostSearch represents the model behind the search form of `common\models\SocailMediaPost`.
 */
class SocialMediaPostSearch extends SocialMediaPost
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'post_section', 'post_id', 'social_media_type', 'published_status', 'status', 'created_by', 'updated_by'], 'integer'],
            [['title', 'description', 'hastag', 'schedule_at', 'error_log', 'created_at', 'updated_at', 'published_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = SocialMediaPost::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'post_section' => $this->post_section,
            'post_id' => $this->post_id,
            'social_media_type' => $this->social_media_type,
            'schedule_at' => $this->schedule_at,
            'published_status' => $this->published_status,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'created_by' => $this->created_by,
            'updated_at' => $this->updated_at,
            'updated_by' => $this->updated_by,
            'published_at' => $this->published_at,
        ]);

        $query->andFilterWhere(['like', 'title', $this->title])
            ->andFilterWhere(['like', 'description', $this->description])
            ->andFilterWhere(['like', 'hastag', $this->hastag])
            ->andFilterWhere(['like', 'error_log', $this->error_log]);

        return $dataProvider;
    }
}
