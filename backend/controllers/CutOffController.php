<?php

namespace backend\controllers;

use Yii;
use common\models\CutOff;
use backend\models\CutOffSearch;
use common\helpers\CollegeHelper;
use common\models\College;
use common\models\CollegeContent;
use common\services\CollegeService;
;
use yii\web\Controller;
use yii\web\NotFoundHttpException;

/**
 * CutOffController implements the CRUD actions for CutOff model.
 */
class CutOffController extends Controller
{
    /**
     * Lists all CutOff models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CutOffSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CutOff model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new CutOff model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new CutOff();
        $category = CollegeService::getCategory();
        $collegeArray = [];
        $postData = Yii::$app->request->post();

        if (isset($_FILES['csv_upload'])) {
            $filePath = $_FILES['csv_upload']['tmp_name'];
        }

        if (!empty($filePath) && file_exists($filePath)) {
            //MIME Check
            $csvMimes = ['text/x-comma-separated-values', 'text/comma-separated-values', 'application/octet-stream', 'application/vnd.ms-excel', 'application/x-csv', 'text/x-csv', 'text/csv', 'application/csv', 'application/excel', 'application/vnd.msexcel', 'text/plain'];

            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mime = finfo_file($finfo, $filePath);
            finfo_close($finfo);
            if (in_array($mime, $csvMimes) === false) {
                Yii::$app->session->setFlash('error', 'Invalid CSV File -  Mime Type Mismatch');
                return $this->redirect('create');
            }

            $csvFile = fopen($filePath, 'r');
            $row = 1;
            while (($data = fgetcsv($csvFile, 1000, ',')) !== false) {
                if ($row>1) {
                    $newModel = new CutOff();
                   
                    // $college = College::find()->select('id')->where(['slug' => $data[0]])->one();
                    // if (!$college) {
                    //     continue;
                    // }

                    // $exam = Exam::find()->select('id')->where(['slug' => $data[1]])->one();
                    // if (!$exam) {
                    //     continue;
                    // }

                    // $course = Course::find()->select('id')->where(['slug' => $data[2]])->one();
                    // if (!$course) {
                    //     continue;
                    // }

                    // if (!empty($data[3])) {
                    //     $specialization = Specialization::find()->select(['id'])->where(['slug' => $data[3]])->one();
                    //     if (!$specialization) {
                    //         continue;
                    //     }
                    //     continue;
                    // }

                    $newModel = new CutOff();
                    // $newModel->college_id = $college->id;
                    // $newModel->exam_id = $exam->id;
                    $newModel->college_id = $data[0];
                    $collegeArray[ $data[0]] = $data[0];
                    $newModel->exam_id = $data[1];
                    if (isset($data[2])) {
                        //$newModel->course_id= $course->id;
                        $newModel->course_id = $data[2];
                    }
                    if (isset($data[3])) {
                        $newModel->specialization_id= $data[3];
                        // $newModel->specialization_id= !empty($specialization) ? $specialization->id : '';
                    }

                    $newModel->program_id = isset($data[4]) ? $data[4] : '';
                    $newModel->program_name = $data[5];
                    if (isset($data[6])) {
                        $newModel->category = isset($category[$data[6]]) ? $category[$data[6]] : '';
                    }
                    if (isset($data[7])) {
                        $newModel->gender= isset(CollegeHelper::$cutoffGender[$data[7]]) ? CollegeHelper::$cutoffGender[$data[7]] : '';
                    }
                    if (isset($data[8])) {
                        $newModel->type= isset(CollegeHelper::$cutOffType[$data[8]]) ? CollegeHelper::$cutOffType[$data[8]] : '';
                    }
                    if (isset($data[9])) {
                        $newModel->year = (int)$data[9];
                    }
                    if (isset($data[10])) {
                        $newModel->round = (int)$data[10];
                    }
                    if (isset($data[11])) {
                        $newModel->opening_rank= !empty($data[11]) ? (int)$data[11] : '';
                    }
                    if (isset($data[12])) {
                        $newModel->closing_rank= !empty($data[12]) ? (int)$data[12] : '';
                        ;
                    }
                    if (isset($data[13])) {
                        $newModel->percentile = $data[13];
                    }
                    if (isset($data[14])) {
                        $newModel->closing_score = $data[14];
                    }
                   
                    if (!$newModel->save()) {
                        Yii::$app->session->setFlash('error', sprintf('CSV Import failed at line %d', $row));
                        // return $this->redirect(['create?college_id' . $college->id]);
                        return $this->redirect('create');
                    }
                }
                $row++;
            }

            fclose($csvFile);
            if (!empty($collegeArray)) {
                foreach ($collegeArray as $collegeSlug) {
                    // $collegeId = College::find()->select(['id'])->where(['slug'=>$collegeSlug])->one();
                    $collegeContent = CollegeContent::find()->select(['id','updated_at','content_updated_at'])
                                     ->where(['entity_id'=> $collegeSlug])
                                     ->andWhere(['sub_page'=>'cut-off'])
                                     ->andWhere(['status'=>CollegeContent::STATUS_ACTIVE])
                                     ->one();
                    if (!empty($collegeContent)) {
                        $collegeContent->scenario = CollegeContent::SCENARIO_IMPORTER;
                        if (empty($collegeContent->content_updated_at)) {
                            $collegeContent->content_updated_at =  $collegeContent->updated_at;
                        }
                        $collegeContent->updated_at = date('Y-m-d H:i:s');
                        $collegeContent->save();
                    }
                }
            }
            
            Yii::$app->session->setFlash('success', 'CSV Import was successful.');
            // return $this->redirect(['index?CutOffSearch[college_id]=' . $college->id]);
            return $this->redirect('index');
        }

        return $this->render('create', [
            'model' => $model,
            'college' => $college ?? null
        ]);
    }

    /**
     * Updates an existing CutOff model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing CutOff model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDeleteAll($exam_id, $college_id, $year)
    {
        CutOff::deleteAll(['exam_id' => $exam_id, 'college_id' => $college_id, 'year' => $year]);
        
        return $this->redirect(['home?id=' . $college_id]);
    }

    public function actionHome($id)
    {
        $college = College::find()->where(['id' => $id])->one();
        $searchModel = new CutOffSearch();

        $dataProvider = $searchModel->home(Yii::$app->request->queryParams);

        return $this->render('home', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'college' => $college
        ]);
    }

    
    /**
     * Finds the CutOff model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CutOff the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CutOff::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
