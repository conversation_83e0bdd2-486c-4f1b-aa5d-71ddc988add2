<?php

namespace backend\controllers;

use Yii;
use common\models\SocailMediaPost;
use backend\models\SocialMediaPostSearch;
use Carbon\Carbon;
use common\models\Article;
use common\models\NewsSubdomain;
use common\models\SocialMediaPost;
use frontend\helpers\Url;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;

/**
 * SocailMediaPostController implements the CRUD actions for SocailMediaPost model.
 */
class SocialMediaPostController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all SocailMediaPost models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new SocialMediaPostSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single SocailMediaPost model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new SocailMediaPost model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new SocialMediaPost();

        if ($model->load(Yii::$app->request->post())) {
            $tags = explode(',', $model->hastag);
            $formatted = array_map(function ($tag) {
                $tag = trim($tag);
                return $tag ? '#' . strtolower($tag) : null;
            }, $tags);
            $model->hastag = implode(', ', array_filter($formatted));
            $model->created_by = Yii::$app->user->identity->id;
            $model->published_status = SocialMediaPost::PUBLISHED_STATUS_PENDING;

            if ($model->validate() && $model->save(false)) {
                Yii::$app->session->setFlash('success', 'Created successfully.');
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                Yii::$app->session->setFlash('error', 'Validation failed. Please check your input.');
            }
        }
        return $this->render('create', [
            'model' => $model,
        ]);
    }


    /**
     * Updates an existing SocailMediaPost model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $hashtags = $model->hastag;
        $model->hastag = str_replace('#', '', $hashtags);
        $status = $model->status;
        if ($model->post_section == SocialMediaPost::POST_SECTION_NEWS) {
            $posts = ArrayHelper::map((NewsSubdomain::find()
                ->select(['name', 'id'])
                ->where(['id' => $model->post_id])
                ->active()->all()), 'id', 'name');
            // $model->post_id = $posts[$model->post_id];
        } elseif ($model->post_section == SocialMediaPost::POST_SECTION_ARTICLES) {
            $posts = ArrayHelper::map((Article::find()
                ->select(['title', 'id'])
                ->where(['id' => $model->post_id])
                ->active()->all()), 'id', 'title');
            // $model->post_id = $posts[$model->post_id];
        }

        // dd($posts);
        if ($model->load(Yii::$app->request->post())) {
            $tags = explode(',', $model->hastag);
            $formatted = array_map(function ($tag) {
                $tag = trim($tag);
                return $tag ? '#' . strtolower($tag) : null;
            }, $tags);
            $model->hastag = implode(', ', array_filter($formatted));
            $model->updated_by = Yii::$app->user->identity->id;
            $model->published_status = SocialMediaPost::PUBLISHED_STATUS_PENDING;

            if ($model->validate() && $model->save(false)) {
                Yii::$app->session->setFlash('success', 'Created successfully.');
                if ($status == $model->status) {
                    return $this->redirect(['view', 'id' => $model->id]);
                } else {
                    return $this->redirect(['update', 'id' => $model->id]);
                }
            } else {
                Yii::$app->session->setFlash('error', 'Validation failed. Please check your input.');
            }
        }

        return $this->render('update', [
            'model' => $model,
            'posts' => $posts,
        ]);
    }

    /**
     * Deletes an existing SocailMediaPost model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    // public function actionDelete($id)
    // {
    //     $this->findModel($id)->delete();

    //     return $this->redirect(['index']);
    // }

    /**
     * Finds the SocailMediaPost model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return SocailMediaPost the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = SocialMediaPost::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    public function actionGetPostsBySection()
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $postData =  Yii::$app->request->post();
        if ($postData['section'] == SocialMediaPost::POST_SECTION_NEWS) {
            $posts = ArrayHelper::map((NewsSubdomain::find()
                ->select(['name', 'id'])
                ->active()->all()), 'id', 'name');
        } elseif ($postData['section'] == SocialMediaPost::POST_SECTION_ARTICLES) {
            $posts = ArrayHelper::map((Article::find()
                ->select(['title', 'id'])
                ->active()->all()), 'id', 'title');
        }

        return $posts;
    }

    public function actionPublish()
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $postData =  Yii::$app->request->post();
        $postStatus = 0;

        $getPost = SocialMediaPost::findOne(['id' => $postData['id']]);
        if ($getPost->post_section == SocialMediaPost::POST_SECTION_ARTICLES) {
            $article = Article::findOne(['id' => $getPost->post_id]);
        }
        if ($getPost->post_section == SocialMediaPost::POST_SECTION_NEWS) {
            $article = NewsSubdomain::findOne(['id' => $getPost->post_id]);
        }

        //post to twitter
        if ($getPost->social_media_type == SocialMediaPost::TYPE_TWITTER) {
            $msg = 'Twitter post failed';
            $title = trim($getPost->title);
            $description = trim($getPost->description);
            $hashtags = str_replace(',', '', $getPost->hastag);
            if ($getPost->post_section ==SocialMediaPost::POST_SECTION_ARTICLES) {
                $url = Url::toGetmyuni() . 'articles/' . $article->slug;
            } elseif ($getPost->post_section == SocialMediaPost::POST_SECTION_NEWS) {
                $url = Url::toNewsSubdmainGetmyuni() . $article->slug;
            }
           
            // Combine with character limit (280 chars)
            $tweetText = "{$title}\n{$description}\n{$hashtags}\n{$url}";
            if (strlen($tweetText) > 280) {
                // Trim description if too long
                $maxDescLength = 280 - strlen($title . $hashtags . $url) - 10;
                $description = mb_substr($description, 0, $maxDescLength) . '...';
                $tweetText = "{$title}\n{$description}\n{$hashtags}\n{$url}";
            }

            $response = Yii::$app->twitter->postTweet($tweetText);
            // echo"<pre>";print_r($response);print_r($response['httpCode']);echo"</pre>";
            $getPost->error_log = json_encode($response['data'] ?? null);
            if ($response['httpCode'] != 201) {
                // Failed
                $getPost->published_status = SocialMediaPost::PUBLISHED_STATUS_FAILED;
                Yii::$app->session->setFlash('error', 'Twitter post failed.');
                $status = 0;
                $msg = 'Twitter post failed';
            } else {
                // Success
                $tweetId   = $response['data']['data']['id'];
                $tweetText = $response['data']['data']['text'];
                $tweetUrl  = "https://twitter.com/getmyuniedu/status/{$tweetId}";

                $getPost->published_status = SocialMediaPost::PUBLISHED_STATUS_POSTED;
                $getPost->published_at     = Carbon::now();
                $getPost->published_by     = Yii::$app->user->identity->id;

                $status = 1;
                $msg    = "Tweet posted successfully: <a href='{$tweetUrl}' target='_blank'>{$tweetUrl}</a>";
                Yii::$app->session->setFlash('success', $msg);
            }
            if ($getPost->save()) {
                return ['status' => $postStatus, 'message' => $msg];
            }
        }
    }
}
