<?php

namespace backend\controllers;

use Yii;
use common\models\PopularCollege;
use backend\models\PopularCollegeSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;
use yii\web\Response;
use yii\data\ActiveDataProvider;
use common\models\College;
use common\models\Stream;
use common\models\Degree;
use common\models\City;
use common\models\State;

/**
 * PopularCollegeController implements the CRUD actions for PopularCollege model.
 */
class PopularCollegeController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all PopularCollege models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new PopularCollegeSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single PopularCollege model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new PopularCollege model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new PopularCollege();

        if ($model->load(Yii::$app->request->post())) {
            $postData = Yii::$app->request->post();
            $collegeIds = $postData['PopularCollege']['college_id'] ?? [];

            // Handle multiple college selection
            if (is_array($collegeIds) && count($collegeIds) > 0) {
                $transaction = Yii::$app->db->beginTransaction();
                $successCount = 0;
                $errors = [];

                try {
                    foreach ($collegeIds as $collegeId) {
                        // Check if combination already exists
                        $existing = PopularCollege::find()
                            ->where([
                                'college_id' => $collegeId,
                                'stream_id' => $model->stream_id,
                                'degree_id' => $model->degree_id,
                                'state_id' => $model->state_id
                            ])
                            ->exists();

                        if (!$existing) {
                            $newModel = new PopularCollege();
                            $newModel->attributes = $model->attributes;
                            $newModel->college_id = $collegeId;

                            if ($newModel->save()) {
                                $successCount++;
                            } else {
                                $college = College::findOne($collegeId);
                                $collegeName = $college ? $college->name : "College ID: $collegeId";
                                $errors[] = "Failed to save tag for: $collegeName";
                            }
                        } else {
                            $college = College::findOne($collegeId);
                            $collegeName = $college ? $college->name : "College ID: $collegeId";
                            $errors[] = "Tag already exists for: $collegeName";
                        }
                    }

                    if ($successCount > 0) {
                        $transaction->commit();
                        Yii::$app->session->setFlash('success', "Successfully created $successCount tag(s).");

                        if (!empty($errors)) {
                            Yii::$app->session->setFlash('warning', 'Some tags were skipped: ' . implode(', ', $errors));
                        }

                        return $this->redirect(['index']);
                    } else {
                        $transaction->rollBack();
                        Yii::$app->session->setFlash('error', 'No tags were created. ' . implode(', ', $errors));
                    }
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    Yii::$app->session->setFlash('error', 'Error creating tags: ' . $e->getMessage());
                }
            } else {
                // Single college selection (fallback)
                if ($model->save()) {
                    return $this->redirect(['view', 'id' => $model->id]);
                }
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing PopularCollege model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing PopularCollege model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Bulk upload popular colleges via CSV
     * @return mixed
     */
    public function actionBulkUpload()
    {
        $model = new \yii\base\DynamicModel(['csvFile']);
        $model->addRule(['csvFile'], 'file', [
            'extensions' => ['csv'],
            'maxSize' => 1024 * 1024 * 5, // 5MB max
            'checkExtensionByMimeType' => false // Allow .csv files even if MIME type is different
        ]);

        if (Yii::$app->request->isPost) {
            $model->csvFile = UploadedFile::getInstance($model, 'csvFile');

            if ($model->csvFile) {
                // Additional check for CSV file extension
                $fileName = $model->csvFile->name;
                $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

                if ($fileExtension !== 'csv') {
                    $model->addError('csvFile', 'Only CSV files are allowed. Your file: ' . $fileName);
                }
            }

            if ($model->validate()) {
                $filePath = $model->csvFile->tempName;
                $result = $this->processCsvFile($filePath);

                Yii::$app->session->setFlash($result['success'] ? 'success' : 'error', $result['message']);

                if ($result['success']) {
                    return $this->redirect(['index']);
                }
            }
        }

        return $this->render('bulk-upload', [
            'model' => $model,
        ]);
    }

    /**
     * Process CSV file for bulk upload
     * @param string $filePath
     * @return array
     */
    private function processCsvFile($filePath)
    {
        $successCount = 0;
        $errorCount = 0;
        $errors = [];
        $rowCount = 0;

        if (($handle = fopen($filePath, 'r')) !== false) {
            // Read header row
            fgetcsv($handle, 1000, ',');

            // Count total rows first
            $totalRows = 0;
            while (($data = fgetcsv($handle, 1000, ',')) !== false) {
                $totalRows++;
            }

            // Check if more than 100 records
            if ($totalRows > 100) {
                fclose($handle);
                return [
                    'success' => false,
                    'message' => "File rejected: Contains $totalRows records. Maximum allowed is 100 records. Please reduce the number of records and try again."
                ];
            }

            // Reset file pointer to beginning and skip header again
            rewind($handle);
            fgetcsv($handle, 1000, ',');

            // Expected columns: college_id, stream_id, level_id, city_id, state_id
            $expectedColumns = ['college_id', 'stream_id', 'level_id', 'city_id', 'state_id'];

            while (($data = fgetcsv($handle, 1000, ',')) !== false) {
                $rowCount++;

                if (count($data) < count($expectedColumns)) {
                    $errorCount++;
                    $errors[] = "Row $rowCount has insufficient columns";
                    continue;
                }

                $rowData = array_combine($expectedColumns, $data);

                // Validate IDs are numeric
                $collegeId = trim($rowData['college_id']);
                $streamId = trim($rowData['stream_id']);
                $levelId = trim($rowData['level_id']);
                $cityId = trim($rowData['city_id']);
                $stateId = trim($rowData['state_id']);

                if (!is_numeric($collegeId) || !is_numeric($streamId) || !is_numeric($levelId) ||
                    !is_numeric($cityId) || !is_numeric($stateId)
                ) {
                    $errorCount++;
                    $errors[] = "Row $rowCount: All IDs must be numeric values";
                    continue;
                }

                // Validate that records exist in respective tables
                $college = College::findOne($collegeId);
                $stream = Stream::findOne($streamId);
                $degree = Degree::findOne($levelId);
                $city = City::findOne($cityId);
                $state = State::findOne($stateId);

                if (!$college) {
                    $errorCount++;
                    $errors[] = "Row $rowCount: College with ID $collegeId not found";
                    continue;
                }
                if (!$stream) {
                    $errorCount++;
                    $errors[] = "Row $rowCount: Stream with ID $streamId not found";
                    continue;
                }
                if (!$degree) {
                    $errorCount++;
                    $errors[] = "Row $rowCount: Level with ID $levelId not found";
                    continue;
                }
                if (!$city) {
                    $errorCount++;
                    $errors[] = "Row $rowCount: City with ID $cityId not found";
                    continue;
                }
                if (!$state) {
                    $errorCount++;
                    $errors[] = "Row $rowCount: State with ID $stateId not found";
                    continue;
                }

                // Check if record already exists
                $existing = PopularCollege::find()
                    ->where([
                        'college_id' => $collegeId,
                        'stream_id' => $streamId,
                        'degree_id' => $levelId,
                        'city_id' => $cityId,
                        'state_id' => $stateId
                    ])
                    ->one();

                if ($existing) {
                    $errorCount++;
                    $errors[] = "Row $rowCount: Record already exists for college: " . $college->name;
                    continue;
                }

                // Get next position for this college
                $maxPosition = PopularCollege::find()
                    ->where(['college_id' => $collegeId])
                    ->max('position');
                $nextPosition = $maxPosition ? $maxPosition + 1 : 1;

                // Create new record
                $popularCollege = new PopularCollege();
                $popularCollege->college_id = $collegeId;
                $popularCollege->stream_id = $streamId;
                $popularCollege->degree_id = $levelId;
                $popularCollege->city_id = $cityId;
                $popularCollege->state_id = $stateId;
                $popularCollege->position = $nextPosition;
                $popularCollege->status = 1;

                if ($popularCollege->save()) {
                    $successCount++;
                } else {
                    $errorCount++;
                    $errors[] = "Row $rowCount: Failed to save record for college: " . $college->name;
                }
            }
            fclose($handle);
        }

        $message = "Upload completed. Success: $successCount, Errors: $errorCount";
        if (!empty($errors)) {
            $message .= "\nErrors: " . implode(', ', array_slice($errors, 0, 5));
            if (count($errors) > 5) {
                $message .= ' and ' . (count($errors) - 5) . ' more...';
            }
        }

        return [
            'success' => $successCount > 0,
            'message' => $message
        ];
    }

    /**
     * Bulk activate selected items
     */
    public function actionBulkActivate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $ids = Yii::$app->request->post('ids', []);
            $count = PopularCollege::updateAll(['status' => 1], ['id' => $ids]);
            $activeRecords = PopularCollege::find()->where(['status' => PopularCollege::STATUS_ACTIVE])->count();
            $inactiveRecords = PopularCollege::find()->where(['status' => PopularCollege::STATUS_INACTIVE])->count();
            return ['success' => true, 'count' => $count, 'active' => $activeRecords, 'inactive' => $inactiveRecords];
        }

        return ['success' => false];
    }

    /**
     * Bulk deactivate selected items
     */
    public function actionBulkDeactivate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $ids = Yii::$app->request->post('ids', []);
            $count = PopularCollege::updateAll(['status' => 0], ['id' => $ids]);
            $activeRecords = PopularCollege::find()->where(['status' => PopularCollege::STATUS_ACTIVE])->count();
            $inactiveRecords = PopularCollege::find()->where(['status' => PopularCollege::STATUS_INACTIVE])->count();
            return ['success' => true, 'count' => $count, 'active' => $activeRecords, 'inactive' => $inactiveRecords];
        }

        return ['success' => false];
    }

    /**
     * Finds the PopularCollege model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return PopularCollege the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = PopularCollege::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
