<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\CutOff */

$this->title = $model->id;
$this->params['breadcrumbs'][] = ['label' => 'Cut Offs', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="cut-off-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'id',
                [
                    'label' => 'College',
                    'value' => $model->college->name ?? '',
                ],
                [
                    'label' => 'Exam',
                    'value' => $model->exam->display_name ?? '',
                ],
                [
                    'label' => 'Course Name',
                    'value' => $model->course->short_name ?? '',
                ],
                [
                    'label' => 'Specialization Name',
                    'value' => $model->specialization ? ($model->specialization->display_name ?: $model->specialization->name) : '',
                ],
                [
                    'label' => 'Category',
                    'value' => $model->categoryName->name ?? '',
                ],
                'gender',
                'type',
                'year',
                'round',
                'opening_rank',
                'closing_rank',
                'percentile',
                'closing_score',
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>