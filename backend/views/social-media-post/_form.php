<?php

use common\helpers\DataHelper;
use common\models\SocialMediaPost;
use kartik\datetime\DateTimePicker;
use kartik\select2\Select2;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\SocialMediaPost */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="Social-media-post-form  box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="row">
        <div class="box-body table-responsive">
            <div class="col-md-12">
                <div class="col-md-4">
                    <?= $form->field($model, 'post_section')->dropDownList(DataHelper::getConstantList('POST_SECTION', SocialMediaPost::class), ['prompt' => 'Select Section', 'id' => 'post-section']) ?>
                </div>
                <div class="col-md-4">
                    <?= $form->field($model, 'post_id')->widget(Select2::class, [
                        'data' => isset($posts) ? $posts : [],
                        'options' => [
                            'placeholder' => 'Select Post',
                            'id' => 'post-id',
                            'disabled' => true,
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                        ],
                    ]) ?>

                </div>
                <div class="col-md-4">
                    <?= $form->field($model, 'social_media_type')->dropDownList(DataHelper::getConstantList('TYPE', SocialMediaPost::class)) ?>
                </div>
            </div>
            <div class="col-md-12">
                <?= $form->field($model, 'title')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-12">
                <?= $form->field($model, 'description')->textarea(['rows' => 6]) ?>
            </div>
            <div class="col-md-12">
                <?= $form->field($model, 'hastag')->textInput(['maxlength' => true, 'placeholder' => 'Enter hashtags separated by commas, e.g. hastag1, hastag2, hastag3'])->label('Hastag (comma seperated)') ?>
            </div>
            <div class="col-md-12">
                <?= $form->field($model, 'schedule_at')->widget(
                    DateTimePicker::class,
                    [
                        'options' => ['placeholder' => 'Select Post Date'],
                        'pluginOptions' => [
                            'timePicker' => true,
                            'locale' => [
                                'format' => 'yyyy-mm-dd HH:ii',
                            ],
                            'autoclose' => true,
                            'todayHighlight' => true,
                        ],
                    ]
                ); ?>
            </div>
            <div class="col-md-12">
                <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', SocialMediaPost::class)) ?>
            </div>
        </div>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', [
            'class' => 'btn btn-success',
            'disabled' => ($model->published_status == SocialMediaPost::PUBLISHED_STATUS_POSTED),
        ]) ?>
        <?php ActiveForm::end(); ?>
        <?php if ($model->status === SocialMediaPost::STATUS_ACTIVE && $model->published_status != SocialMediaPost::PUBLISHED_STATUS_POSTED): ?>
            <button type="button" class="btn btn-primary" id="publish" data-value=<?= $model->id; ?>>Publish</button>
        <?php endif; ?>
    </div>
</div>
<?php

$fetchUrl = \yii\helpers\Url::to(['/social-media-post/get-posts-by-section']); // Update controller/action as per your structure
$script = <<<JS
$(document).ready(function() {
    // Initialize Select2 (in case needed)
    $('#post-id').select2({
        placeholder: 'Select Post',
        allowClear: true
    });

    $('#post-section').on('change', function() {
        var section = $(this).val();
        var \$postId = $('#post-id');

        // Clear current options and disable
        \$postId.val(null).trigger('change');
        \$postId.html('').trigger('change');
        \$postId.prop('disabled', true);

        if (section) {
            $.ajax({
                url: '{$fetchUrl}',
                type: 'Post',
                data: { section: section },
                success: function(data) {
                    var newOptions = '<option></option>'; // for placeholder
                    $.each(data, function(id, text) {
                        newOptions += '<option value="' + id + '">' + text + '</option>';
                    });

                    // Replace the options in Select2
                    \$postId.html(newOptions);
                    \$postId.prop('disabled', false);
                    \$postId.trigger('change');
                },
                error: function() {
                    alert('Failed to load posts.');
                }
            });
        }
    });

    $('#publish').on('click', function(e) {
        e.preventDefault();
        var confirmPublish = confirm('Are you sure you want to publish this post? You cannot change anything after posting');

        if (!confirmPublish) {
            return; // Exit if user cancels
        }
        var id= $('#publish').attr('data-value');
        $('#publish').text('Please Wait').attr('disabled', true);
        $.ajax({
            url: '/social-media-post/publish',
            type: 'POST',
            data: {
                id: id
            },
            success: function(data) {
                if (data) {
                    location.reload(); // 🔄 Page will reload, and flash will show
                }
            },
            error: function () {
                // alert('Unexpected error occurred.');
            }
        });
    });
});
JS;
$this->registerJs($script);
?>