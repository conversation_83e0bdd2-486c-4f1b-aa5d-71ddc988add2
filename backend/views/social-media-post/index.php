<?php

use common\helpers\DataHelper;
use common\models\SocialMediaPost;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\SocailMediaPostSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Socail Media Posts';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="socail-media-post-index box box-primary">
    <div class="box-header with-border">
       <?= Html::a('Create', ['create'], ['class' => 'btn btn-success']) ?>
    </div>
    <div class="box-body table-responsive no-padding">

    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'layout' => "{items}\n{summary}\n{pager}",
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],
            [
                'attribute' => 'post_section',
                'value' => function ($model) {
                    return ArrayHelper::getValue(DataHelper::getConstantList('POST_SECTION', SocialMediaPost::class), $model->post_section);
                },
                'filter' => DataHelper::getConstantList('POST_SECTION', SocialMediaPost::class),
            ],
             [
                'attribute' => 'post_id',
                'label' => 'Post Title',
                'filter' => false,
                'value' => function ($model) {
                    switch ($model->post_section) {
                        case SocialMediaPost::POST_SECTION_NEWS:
                            return $model->news ? $model->news->name : '(Not found)';
                        case SocialMediaPost::POST_SECTION_ARTICLES:
                            return $model->articles ? $model->articles->title : '(Not found)';
                        default:
                            return $model->post_id;
                    }
                }
            ],
            [
                'attribute' => 'social_media_type',
                'value' => function ($model) {
                    return ArrayHelper::getValue(DataHelper::getConstantList('TYPE', SocialMediaPost::class), $model->social_media_type);
                },
                'filter' => DataHelper::getConstantList('TYPE', SocialMediaPost::class),
            ],
            'title',
            //'description:ntext',
            'hastag',
            'schedule_at',
            //'published_status',
            //'error_log',
            [
                'attribute' => 'status',
                'value' => function ($model) {
                    return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SocialMediaPost::class), $model->status);
                },
                'filter' => DataHelper::getConstantList('STATUS', SocialMediaPost::class),
            ],
            'created_at:date',
            // 'created_by',
            'updated_at:date',
             // 'updated_by',
            'published_at:date',
            [
                'class' => 'yii\grid\ActionColumn',
                'template' => '{view} {update}',
            ],
        ],
    ]); ?>
    </div>
    </div>
