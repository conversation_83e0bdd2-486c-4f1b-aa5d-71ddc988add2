<?php

use common\helpers\DataHelper;
use common\models\SocialMediaPost;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\SocailMediaPost */

$this->title = $model->title;
$this->params['breadcrumbs'][] = ['label' => 'Socail Media Posts', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);
?>
<div class="socail-media-post-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'id',
            [
                'attribute' => 'post_section',
                'value' => function ($model) {
                    return ArrayHelper::getValue(DataHelper::getConstantList('POST_SECTION', SocialMediaPost::class), $model->post_section);
                }
            ],
            [
                'attribute' => 'post_id',
                'label' => 'Post Title',
                'value' => function ($model) {
                    switch ($model->post_section) {
                        case SocialMediaPost::POST_SECTION_NEWS:
                            return $model->news ? $model->news->name : '(Not found)';
                        case SocialMediaPost::POST_SECTION_ARTICLES:
                            return $model->articles ? $model->articles->title : '(Not found)';
                        // add more cases as needed
                        default:
                            return $model->post_id;
                    }
                }
            ],
            [
                'attribute' => 'social_media_type',
                'value' => function ($model) {
                    return ArrayHelper::getValue(DataHelper::getConstantList('TYPE', SocialMediaPost::class), $model->social_media_type);
                }
            ],
            'title',
            'description:ntext',
            'hastag',
            'schedule_at',
            'published_status',
            'error_log',
            [
                'attribute' => 'status',
                'value' => function ($model) {
                    return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SocialMediaPost::class), $model->status);
                }
            ],
            'created_at',
            [
                'attribute' => 'created_by',
                'value' => function ($model) {
                    return $model->createdUser->email ?? '-';
                }
            ],
            'updated_at',
            'updated_by',
            [
                'attribute' => 'updated_by',
                'value' => function ($model) {
                    return $model->UpdatedUser->email ?? '-';
                }
            ],
            'published_at',
        ],
    ]) ?>
    </div>
</div>
