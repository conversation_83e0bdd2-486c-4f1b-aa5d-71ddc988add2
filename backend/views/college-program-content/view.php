<?php

use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\CollegeProgramContent;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

// dd($model->getAllProgram());
/* @var $this yii\web\View */
/* @var $model common\models\CollegeProgramContent */

$this->title = $model->collegeCourse->college ? ($model->collegeCourse->college->name ?? $model->collegeCourse->college->display_name) : $model->id;
$this->params['breadcrumbs'][] = ['label' => 'College Program Contents', 'url' => ['index']];
$this->params['breadcrumbs'][] = ['label' => ($model->collegeCourse->college ? ($model->collegeCourse->college->name ?? $model->collegeCourse->college->display_name) : $model->id), 'url' => '/college/view?id=' . $model->collegeCourse->college->id];
$this->params['breadcrumbs'][] = $model->id;
?>
<div class="college-program-content-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php if (!empty($model->collegeCourse->college)): ?>
            <?= Html::a('Index', ['college-course/index', 'CollegeCourseSearch[college_id]' => $model->collegeCourse->college->id, 'CollegeCourseSearch[course_id]' => $model->collegeCourse->course_id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php endif; ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                [
                    'attribute' => 'college_course_id',
                    'label' => 'Program Name',
                    'value' => function ($model) {
                        return $model->programName ? $model->programName[0]['name'] : $model->college_course_id;
                    }
                ],
                [
                    'label' => 'Qualification',
                    'format' => 'raw',
                    'value' => function ($model) {
                        return $this->render('_qualification', ['model' => $model]);
                    }
                ],
                [
                    'label' => 'Content',
                    'format' => 'raw',
                    'value' => ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->content))),
                ],
                'meta_title',
                'meta_description',
                'author.name',
                'h1',
                [
                    'label' => 'Content Template',
                    'format' => 'raw',
                    'value' => function ($model) {
                        return $model->contentTemplate ?
                        ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->contentTemplate->content)))
                        : '';
                    }
                ],
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CollegeProgramContent::class), $model->status)
                ],
            ],
        ]) ?>
    </div>
</div>