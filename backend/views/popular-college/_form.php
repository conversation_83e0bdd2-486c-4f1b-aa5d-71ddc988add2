<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\web\JsExpression;
use common\models\College;
use common\models\Stream;
use common\models\Degree;
use common\models\City;
use common\models\State;
use common\helpers\DataHelper;
use common\models\PopularCollege;

/* @var $this yii\web\View */
/* @var $model common\models\PopularCollege */
/* @var $form yii\widgets\ActiveForm */

$colleegData = !empty($model->college_id) ? ArrayHelper::map(College::find()->where(['id' => $model->college_id])->all(), 'id', 'name') : [];
?>
<style>
    .popular-college-form .box-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 10px;
    }

    .popular-college-form .box-header h3 {
        color: white;
        margin: 0;
        font-weight: 600;
        font-size: 20px;
    }

    .help-text {
        font-size: 0.9em;
        color: #666;
        margin-top: 5px;
    }

    .validation-summary {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .popular-college-create,
    .popular-college-update {
        margin-top: 20px
    }

    #popularcollege-status,
    #popularcollegecollege-position {
        border-radius: 5px;
    }

    .disable-select2+.select2-container {
        pointer-events: none;
        opacity: 0.6;
    }

    .content-header {
        display: none;
    }
</style>

<div class="popular-college-form">
    <div class="box box-primary">
        <div class="box-header with-border">
            <h3><i class="fa fa-tag"></i> <?= $model->isNewRecord ? 'Create Popular College' : 'Update Popular College' ?></h3>
        </div>

        <?php $form = ActiveForm::begin([
            'options' => ['class' => 'form-horizontal'],
            'fieldConfig' => [
                'template' => '<div class="col-sm-3 control-label">{label}</div><div class="col-sm-9">{input}{error}{hint}</div>',
                'labelOptions' => ['class' => 'control-label'],
            ],
        ]); ?>
        <div class="box-body">
            <div class="row">
                <div class="col-md-6">
                    <?= $form->field($model, 'college_id')->widget(Select2::class, [
                        'data' => $colleegData,
                        'options' => [
                            'placeholder' => '--Select Colleges--',
                            'multiple' => $model->isNewRecord,
                            'id' => 'popularcollegecollege-college_id',
                            'class' => $model->isNewRecord ? '' : 'disable-select2'
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                            'minimumInputLength' => 3,
                            'language' => [
                                'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                                'inputTooShort' => new JsExpression("function () { return 'Please enter 3 or more characters'; }"),
                                'searching' => new JsExpression("function () { return 'Searching colleges...'; }"),
                            ],
                            'ajax' => [
                                'url' => ['../ajax/college-list'],
                                'dataType' => 'json',
                                'data' => new JsExpression('function(params) {return {q:params.term}; }')
                            ],
                            'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                            'templateResult' => new JsExpression('function(data) { return data.text; }'),
                            'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                        ],
                    ])->label('Colleges <span class="text-danger">*</span>'); ?>
                </div>

                <div class="col-md-6">
                    <?= $form->field($model, 'stream_id')->widget(Select2::class, [
                        'data' => ArrayHelper::map(Stream::find()
                            ->where(['not in', 'slug', ['other']])
                            ->andWhere(['status' => Stream::STATUS_ACTIVE])->orderBy('name ASC')->all(), 'id', 'name'),
                        'options' => [
                            'placeholder' => '--Select Stream--',
                            'multiple' => false,
                            'id' => 'popularcollegecollege-stream_id'
                        ],
                        'pluginOptions' => [
                            'allowClear' => true
                        ],
                    ])->label('Stream <span class="text-danger">*</span>'); ?>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <?= $form->field($model, 'degree_id')->widget(Select2::class, [
                        'data' => ArrayHelper::map(Degree::find()
                            ->where(['not in', 'slug', ['tenth', 'eleventh', 'twelfth', 'other']])
                            ->andWhere(['status' => Degree::STATUS_ACTIVE])->orderBy('name ASC')->all(), 'id', 'name'),
                        'options' => [
                            'placeholder' => '--Select Degree/Level--',
                            'multiple' => false,
                            'id' => 'popularcollegecollege-degree_id'
                        ],
                        'pluginOptions' => [
                            'allowClear' => true
                        ],
                    ])->label('Degree/Level <span class="text-danger">*</span>'); ?>
                </div>

                <div class="col-md-6">
                    <?= $form->field($model, 'state_id')->widget(Select2::class, [
                        'data' => ArrayHelper::map(State::find()->where(['status' => State::STATUS_ACTIVE])->orderBy('name ASC')->all(), 'id', 'name'),
                        'options' => [
                            'placeholder' => '--Select State--',
                            'multiple' => false,
                            'id' => 'popularcollegecollege-state_id'
                        ],
                        'pluginOptions' => [
                            'allowClear' => true
                        ],
                    ])->label('State <span class="text-danger">*</span>'); ?>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <?= $form->field($model, 'city_id')->widget(Select2::class, [
                        'data' => $model->city_id ? [$model->city_id => $model->city ? $model->city->name : ''] : [],
                        'options' => [
                            'placeholder' => '--Select City--',
                            'multiple' => false,
                            'id' => 'popularcollegecollege-city_id',
                            'disabled' => $model->isNewRecord,
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                            'minimumInputLength' => 2,
                            'language' => [
                                'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                                'inputTooShort' => new JsExpression("function () { return 'Please enter 2 or more characters'; }"),
                                'searching' => new JsExpression("function () { return 'Searching cities...'; }"),
                            ],
                            'ajax' => [
                                'url' => ['../ajax/statewise-city-list'],
                                'dataType' => 'json',
                                'data' => new JsExpression('function(params) {return {q:params.term, ids:$("#popularcollegecollege-state_id").val()}; }')
                            ],
                            'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                            'templateResult' => new JsExpression('function(data) { return data.text; }'),
                            'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                        ],
                    ])->label('City'); ?>
                </div>

                <?= $form->field($model, 'position')->hiddenInput([
                    'type' => 'hidden',
                    'min' => 1,
                    'max' => 100,
                    'id' => 'popularcollegecollege-position',
                    'class' => 'form-control',
                    'value' => $model->position ?? 1, // default to 1 if empty
                ])->label(''); ?>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', PopularCollege::class))->label('Status <span class="text-danger">*</span>'); ?>
                </div>
            </div>

            <!-- College Selection Info -->
            <div id="college-count-info" class="alert alert-info" style="display: none; margin-top: 15px;">
            </div>

        </div>

        <div class="box-footer">
            <div class="row">
                <div class="col-md-8">
                    <?= Html::submitButton($model->isNewRecord ? '<i class="fa fa-save"></i> Create' : '<i class="fa fa-save"></i> Update', [
                        'class' => 'btn btn-success btn-flat btn-lg',
                        'style' => 'margin-right: 10px;padding:5px 16px;'
                    ]) ?>
                    <?= Html::a('<i class="fa fa-list"></i> Index', ['index'], [
                        'class' => 'btn btn-success btn-flat btn-lg',
                        'style' => 'margin-right: 10px;padding:5px 16px;'
                    ]) ?>
                </div>
            </div>
        </div>
        <?php ActiveForm::end(); ?>
    </div>
</div>

<?php
//enable/disable city select2 based on state selection
$script = <<<JS
$(document).ready(function() {
    $('#popularcollegecollege-state_id').on('change', function() {
        if(this.value !== '') {
            $('#popularcollegecollege-city_id').prop('disabled', false);
        } else {
            $('#popularcollegecollege-city_id').prop('disabled', true);
        }
    });
});
JS;
$this->registerJs($script);
?>