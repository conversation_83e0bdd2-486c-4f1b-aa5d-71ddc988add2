<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\PopularCollege */

$this->title = 'View Popular College';
$this->params['breadcrumbs'][] = ['label' => 'Popular Colleges', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<style>
    .popular-college-view .box-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 10px;
    }

    .popular-college-view .box-header h3 {
        color: white;
        margin: 0;
        font-weight: 600;
        font-size: 20px;
    }

    .content-header {
        display: none;
    }

    .box-footer {
        padding: 15px;
    }
</style>
<div class="popular-college-view box box-primary">
    <div class="box-header with-border">
        <h3><i class="fa fa-tag"></i> View Popular College</h3>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                [
                    'attribute' => 'stream_id',
                    'label' => 'Stream',
                    'value' => function ($model) {
                        return $model->stream ? $model->stream->name : 'N/A';
                    }
                ],
                [
                    'attribute' => 'degree_id',
                    'label' => 'Degree',
                    'value' => function ($model) {
                        return $model->degree ? $model->degree->name : 'N/A';
                    }
                ],
                [
                    'attribute' => 'city_id',
                    'label' => 'City',
                    'value' => function ($model) {
                        return $model->city ? $model->city->name : 'N/A';
                    }
                ],
                [
                    'attribute' => 'state_id',
                    'label' => 'State',
                    'value' => function ($model) {
                        return $model->state ? $model->state->name : 'N/A';
                    }
                ],
                'status',
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>

        <div class="box-footer">
            <div class="row">
                <div class="col-md-8">
                    <?= Html::a('<i class="fa fa-edit"></i> Update', ['update', 'id' => $model->id], [
                        'class' => 'btn btn-success btn-flat btn-lg',
                        'style' => 'margin-right: 10px;padding:5px 16px;'
                    ]) ?>
                    <?= Html::a('<i class="fa fa-list"></i> Index', ['index'], [
                        'class' => 'btn btn-success btn-flat btn-lg',
                        'style' => 'margin-right: 10px;padding:5px 16px;'
                    ]) ?>
                </div>
            </div>
        </div>
    </div>
</div>