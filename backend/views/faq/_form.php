<?php

use common\helpers\DataHelper;
use common\models\Faq;
use kartik\depdrop\DepDrop;
use kartik\select2\Select2;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use unclead\multipleinput\MultipleInput;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\web\JsExpression;
use dosamigos\tinymce\TinyMce;

/* @var $this yii\web\View */
/* @var $model common\models\Faq */
/* @var $form yii\widgets\ActiveForm */

$entitiesArr = DataHelper::$entities;
if (($key = array_search('NEWS', $entitiesArr)) !== false) {
    unset($entitiesArr[$key]);
}
?>

<div class="faq-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">
        <?= $form->field($model, 'entity')->dropDownList($entitiesArr, [
            'id' => 'entity',
            'placeholder' => 'Select..',
            'disabled' => !$model->isNewRecord,
            'onchange' => 'if (this.value.toLowerCase() == "exam") {
                $("#faq-sub_page").attr("disabled", false);
            } else if (this.value.toLowerCase() == "filter") {
                $("#faq-sub_page").attr("disabled", true);
                $("#faq-entity_id").attr("disabled", true);
                $("#faq-page").attr("type", "text");                
            } else {
                $("#faq-sub_page").attr("disabled", true);
            }'
        ])->label('Type') ?>

        <?= $form->field($model, 'entity_id')->widget(Select2::classname(), [
            'data' => !empty($model->pageName) ? ArrayHelper::map($model->pageName, 'id', 'name') : [],
            'language' => 'en',
            'options' => [
                'placeholder' => '--Select--',
                'multiple' => false,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'placeholder' => '--Select--',
                'disabled' => !$model->isNewRecord,
                'minimumInputLength' => 3,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => Url::to(['/faq/get-list']),
                    'dataType' => 'json',
                    'data' => new JsExpression("function(params) {return {q:params.term,depdrop_parents:$('#entity').val()}; }")
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Select Page');
?>


        <?= $form->field($model, 'page')->textInput(['type' => $model->isNewRecord ? 'hidden' : 'text', 'disabled' => !$model->isNewRecord])->label(true, ['class' => 'hidden']); ?>

        <!-- to do -->
        <?= $form->field($model, 'sub_page')->widget(DepDrop::class, [
            'type' => DepDrop::TYPE_SELECT2,
            'options' => [
                'id' => 'sub_page',
                'placeholder' => '--Select--',
                'multiple' => false,
            ],
            'data' => !empty($model->subPage) ? ArrayHelper::map($model->subPage, 'id', 'name') : [],
            'select2Options' => ['pluginOptions' => [
                'allowClear' => true,
                'disabled' => !$model->isNewRecord ?? (strtolower($model->entity) == 'article') ? true : false
            ]],
            'pluginOptions' => [
                'depends' => ['entity'],
                'placeholder' => 'Select...',
                'url' => Url::to(['/faq/get-sub-page'])
            ],
        ])->label('Sub Page');
?>

        <?= $form->field($model, 'child_sub_page')->widget(Select2::classname(), [
            'data' => !empty($model->subPage) ? ArrayHelper::map($model->subPage, 'id', 'name') : [],
            'language' => 'en',
            'options' => [
                'id' => 'child_sub_page',
                'placeholder' => '--Select--',
                'allowClear' => true,
                'multiple' => false,
            ],
            'pluginOptions' => [
                'disabled' => !$model->isNewRecord,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => Url::to(['/faq/get-dropdown-sub-page']),
                    'dataType' => 'json',
                    'data' => new JsExpression("function(params) {return {q:params.term,entity_id:$('#faq-entity_id').val(),depdrop_parents:$('#entity').val(),depdrop_child:$('#sub_page').val()}; }")
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Dropdown Sub Page');
?>

        <?php /*= $form->field($model, 'sub_page')->dropDownList(DataHelper::examContentList(), [
            'disabled' => (strtolower($model->entity) != 'exam') ? true : false,
        ])*/ ?>

        <div class="text-muted well well-sm no-shadow">

            <?= $form->field($model, 'qnas')->widget(MultipleInput::class, [
                'id' => 'custom-multiple-input-id', // Set a custom ID
                'max'               => 30,
                'allowEmptyList'    => true,
                'enableGuessTitle'  => true,
                'columns' => [
                    [
                        'name'  => 'question',
                        'title' => 'Question',
                        'type' => 'textarea',
                        'columnOptions' => [
                            'style' => 'width: 40%;resize: none;',

                        ],
                    ],
                    [
                        'name'  => 'answer',
                        'title' => 'Answer',
                        'type' => 'textarea',
                        'columnOptions' => [
                            'style' => 'width:58%;resize: none;',
                        ],
                        'options' => [
                            'class' => 'tiny-mce', // Use this class to initialize TinyMCE
                        ],
                    ]
                ]
            ])->label();
?>
        </div>

        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', Faq::class)); ?>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php
    // Register TinyMCE asset
    TinyMce::widget([
        'name' => 'TinyMCE',
        'options' => [
            'class' => 'tiny-mce',
        ],
        'clientOptions' => [
            'plugins' => 'advlist autolink lists link image charmap print preview anchor',
            'toolbar' => 'undo redo | formatselect | bold italic backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link',
        ],
    ]);

    ActiveForm::end(); ?>
    <?php $this->registerJs(' $("#sub_page").change(function(){
        $("#child_sub_page option").remove();
        var entity_val = $("#entity").val();
        var selected_val = $("#sub_page option:selected").val();
        var entityId = $("#faq-entity_id").val();
        $.ajax({
            type: "GET",
            url: "/faq/get-dropdown-sub-page",
            data: { entity_id: entityId, depdrop_parents: entity_val, depdrop_child: selected_val, subpage_check: "true" },
            dataType: "json",
            success: function (response) {
                if (response > 0) {
                    $(".field-child_sub_page").css("display","block");
                } else {
                    $("#child_sub_page").val(null).trigger("change");
                    $(".field-child_sub_page").css("display","none");
                }
            }
        });
    })');

    $this->registerJs('
    function initializeTinyMCE() {
        tinymce.remove(".tiny-mce");
        tinymce.init({
            selector: ".tiny-mce",
            plugins: "advlist autolink lists link image charmap print preview anchor searchreplace visualblocks code fullscreen insertdatetime media table paste help wordcount",
            toolbar: "undo redo | formatselect | bold italic underline forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist | link",
            menubar: false,
            toolbar_mode: "wrap",
            advlist_bullet_styles: "default,circle,square",
            advlist_number_styles: "default,lower-alpha,lower-roman,upper-alpha,upper-roman",
            rel_list: [
                { title: "", value: "" },
                { title: "No Follow", value: "nofollow" }
            ],
            allow_unsafe_link_target: true
        });
    }

    $(document).ready(function() {
        initializeTinyMCE();

        $(document).on("afterAddRow", function() {
            initializeTinyMCE();
        });
    });
', \yii\web\View::POS_READY);

    ?>
</div>