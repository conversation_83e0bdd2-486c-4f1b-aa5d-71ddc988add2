<?php

namespace api\modules\v1\controllers;

use common\helpers\CollegeHelper;
use common\models\College;
use common\models\CollegeContent;
use Yii;
use yii\web\MethodNotAllowedHttpException;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use common\models\CollegeNotificationUpdate;
use frontend\helpers\Url;
use yii\web\ForbiddenHttpException;

class CollegeNotificationController extends \yii\web\Controller
{
    public function actionCollegeUpdateNotification()
    {
        if (!Yii::$app->request->isPost) {
            throw new MethodNotAllowedHttpException();
        }

        $requests = Yii::$app->request->post();
        $payload = [];
        $source_id = '';
        $result = [];

        foreach ($requests as $value) {
            if (($value['content_type'] == 'gmu_college_notification') && ($value['source'] == 2)) {
                $current_date = date('Y-m-d H:i:s');
                $end_date = date('Y-m-d', strtotime($value['date'] . ' +60 days'));
                $source_id = $value['source_id'];

                $model = CollegeNotificationUpdate::find()
                    ->where([
                        'source_id' => $value['source_id'],
                        'college_id' => $value['institute_id'],
                        'entity_id' => $value['object_id']
                    ])
                    ->one();

                if (!empty($model)) {
                    $query = \Yii::$app->db->createCommand('
                        UPDATE college_notification_update SET 
                            source_id = :source_id,
                            college_id = :college_id,
                            entity_id = :entity_id,
                            `text` = :text,
                            sub_page = :sub_page,
                            content = :content,
                            status = :status,
                            updated_at = :updated_at,
                            start_date = :start_date,
                            end_date = :end_date,
                            publish_at = :publish_at
                        WHERE id = :id
                    ')
                        ->bindValues([
                            ':source_id'   => 2,
                            ':college_id'  => $value['institute_id'] ?? null,
                            ':entity_id'   => $value['object_id'] ?? null,
                            ':text'        => $value['heading'] ?? null,
                            ':sub_page'    => $value['master_page_slug'] ?? null,
                            ':content'     => $value['content'] ?? null,
                            ':status'      => $value['object_status'] ?? null,
                            ':updated_at'  => $current_date,
                            ':start_date'  => $value['date'] ?? null,
                            ':end_date'    => $end_date,
                            ':publish_at'  => $value['date'] ?? null,
                            ':id'          => $value['id'] ?? null,
                        ])
                        ->execute();
                } else {
                    $query = \Yii::$app->db->createCommand('
                        INSERT INTO college_notification_update (
                        source_id,
                        college_id,
                        entity_id,
                        `text`,
                        sub_page,
                        content,
                        status,
                        created_at,
                        updated_at,
                        start_date,
                        end_date,
                        publish_at
                        ) VALUES (
                        :source_id,
                        :college_id,
                        :entity_id,
                        :text,
                        :sub_page,
                        :content,
                        :status,
                        :created_at,
                        :updated_at,
                        :start_date,
                        :end_date,
                        :publish_at
                        )')
                        ->bindValues([
                            ':source_id'   => 2,
                            ':college_id'  => $value['institute_id'],
                            ':entity_id'   => $value['object_id'],
                            ':text'        => $value['heading'],
                            ':sub_page'    => $value['master_page_slug'],
                            ':content'     => $value['content'],
                            ':status'      => $value['object_status'],
                            ':created_at'  => $current_date,
                            ':updated_at'  => $current_date,
                            ':start_date'  => $value['date'],
                            ':end_date'    => $end_date,
                            ':publish_at'  => $value['date'],
                        ])
                        ->execute();
                }

                if ($query > 0) {
                    $college = College::find()->where(['id' => $value['institute_id']])->one();
                    $collegeContent = CollegeContent::find()->where(['entity_id' => $college->id])->andWhere(['sub_page' => $value['master_page_slug']])->one();
                    $url = '';
                    if (($value['master_page_slug'] == 'overview') || ($value['master_page_slug'] == 'info')) {
                        $url = Url::toGetmyuni() . 'college/' . $college->slug;
                        $payload[] = ['gmu_absolute_url' => $url, 'gmu_page_slug' => $value['master_page_slug']];
                    } else {
                        if (!empty($collegeContent->parent_id)) {
                            $url = CollegeHelper::collegeDropDownUrlFormate($college->slug, $value['master_page_slug'], $collegeContent->sub_page);
                        } else {
                            $url = CollegeHelper::collegeUrlFormate($college->slug, $value['master_page_slug']);
                        }
                        $url = Url::toGetmyuni() . 'college/' . $url;
                        $payload[] = ['gmu_absolute_url' => $url, 'gmu_page_slug' => $value['master_page_slug']];
                    }
                }
            }
            $result = ['callback_data' => $payload];
        }

        // Send PATCH request to external API
        $externalUrl = 'http://34.93.68.46/api/notification/gmu-update-callback/' . $source_id;
        $ch = curl_init($externalUrl);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['data' => $result]));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen(json_encode(['data' => $result]))
        ]);

        $response = curl_exec($ch);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError) {
            Yii::error("Curl Error: $curlError", __METHOD__);
        }

        return [
            'status' => 'success',
            'sent_payload' => $result,
            'response_from_patch' => $response,
            'curl_error' => $curlError ?: null,
        ];
    }

    public function actionCollegeContent($inst_id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isGet) {
            throw new MethodNotAllowedHttpException('Only GET method is allowed.');
        }

        $headAuth = Yii::$app->request->headers->get('authorization') ?? '';
        $isAuthPass = $this->checkAuth(md5('college@data123'), $headAuth);

        if ($isAuthPass !== true) {
            throw new ForbiddenHttpException('Invalid authorization.');
        }

        if (empty($inst_id)) {
            throw new BadRequestHttpException('Parameter `inst_id` is required.');
        }

        $collegeContent = CollegeContent::find()
            ->select(['id', 'entity_id', 'sub_page', 'parent_id'])
            ->where(['entity_id' => $inst_id, 'status' => CollegeContent::STATUS_ACTIVE])
            ->asArray()
            ->all();

        if (empty($collegeContent)) {
            throw new NotFoundHttpException("No active content found for inst_id: $inst_id");
        }

        return [
            'status' => 'success',
            'data' => $collegeContent,
        ];
    }

    protected function checkAuth(string $expectedHash, string $authHeader): bool
    {
        return $expectedHash === $authHeader;
    }
}
