<?php

namespace common\components;

use Yii;
use yii\base\Component;
use yii\httpclient\Client;

class TwitterComponent extends Component
{

    public $apiKey;
    public $apiSecret;
    public $accessToken;
    public $accessSecret;

    public function __construct()
    {
        // ⚠️ Replace these with your actual app keys/tokens
        $this->apiKey       = '*************************';
        $this->apiSecret    = 'oCXaQ7Pq5ytSnB4hXQ1rVKBCmiYq9ahrOApveC5T3aWEFeRDsC';
        $this->accessToken  = '972413009904594944-S7Bxumq5T4kYl1loDyuy9eCvNAXABon';
        $this->accessSecret = 'eg0KESVJZjfBjEaprAikrm3nbidt2QqaRGJnJfc1sdeC4';

        parent::__construct();
    }

    private function buildOauthHeader($url, $method, $params)
    {
        $oauth = [
            'oauth_consumer_key'     => $this->apiKey,
            'oauth_nonce'            => bin2hex(random_bytes(16)),
            'oauth_signature_method' => 'HMAC-SHA1',
            'oauth_timestamp'        => time(),
            'oauth_token'            => $this->accessToken,
            'oauth_version'          => '1.0',
        ];

        $baseParams = array_merge($oauth, $params);
        ksort($baseParams);

        $baseString = strtoupper($method) . '&' . rawurlencode($url) . '&' . rawurlencode(http_build_query($baseParams, '', '&', PHP_QUERY_RFC3986));
        $signingKey = rawurlencode($this->apiSecret) . '&' . rawurlencode($this->accessSecret);
        $oauth['oauth_signature'] = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));

        $header = 'OAuth ';
        $values = [];
        foreach ($oauth as $key => $value) {
            $values[] = $key . '="' . rawurlencode($value) . '"';
        }
        $header .= implode(', ', $values);

        return $header;
    }

    public function postTweet($status)
    {
        $client = new Client();
        $url    = 'https://api.twitter.com/2/tweets';

        $params = ['text' => $status];
        $authHeader = $this->buildOauthHeader($url, 'POST', []);

        $response = $client->createRequest()
            ->setMethod('POST')
            ->setUrl($url)
            ->addHeaders([
                'Authorization' => $authHeader,
                'Content-Type'  => 'application/json',
            ])
            ->setContent(json_encode($params))
            ->send();

        return [
            'httpCode' => $response->getStatusCode(),
            'data'     => $response->getData(),
            'raw'      => $response->content,
        ];
    }
}
