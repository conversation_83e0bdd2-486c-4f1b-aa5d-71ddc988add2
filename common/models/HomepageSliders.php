<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use common\models\College;

/**
 * This is the model class for table "homepage_sliders".
 *
 * @property int $id
 * @property int|null $entity_id
 * @property string|null $redirect_link
 * @property string|null $slider_image
 * @property int $status
 * @property string|null $created_at
 * @property string|null $updated_at
 */
class HomepageSliders extends \yii\db\ActiveRecord
{

    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;


    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'homepage_sliders';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['entity_id'], 'required'],
            [['entity_id', 'status', 'position'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['redirect_link'], 'string', 'max' => 255],
            [['entity_id'], 'unique'],
            [['image'], 'image'],
            [['image'], 'image','extensions' => ['webp']],
            [['image'], 'required', 'on' => 'create'],
            // [['image'], 'required', 'when' => function ($model) {
            //     return $model->isNewRecord;
            // }],
            // [['image'], 'image', 'maxWidth' => '1200', 'maxHeight' => '667', 'maxSize' => 1024 * 100, 'extensions' => ['jpg', 'jpeg', 'webp'], 'message' => 'Image size should not be greater than 100kb'],
            //[['image'], 'image', 'maxSize' => 1024 * 100, 'extensions' => ['jpg', 'jpeg', 'webp'], 'message' => 'Image size should not be greater than 100kb'],

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'entity_id' => 'Entity ID',
            'redirect_link' => 'Redirect Link',
            'image' => 'Image',
            'position' => 'Position',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    public function getCollege()
    {
        return $this->hasOne(College::className(), ['id' => 'entity_id']);
    }
}
