<?php

namespace common\models\query;

/**
 * This is the ActiveQuery class for [[\common\models\NewsSubdomain]].
 *
 * @see \common\models\NewsSubdomain
 */
class NewsSubdomainQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return \common\models\NewsSubdomain[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\NewsSubdomain|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }

    public function active()
    {
        return $this->andWhere('[[status]]=1');
    }
}
