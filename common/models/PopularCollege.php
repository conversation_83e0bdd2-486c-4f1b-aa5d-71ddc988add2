<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "popular_college".
 *
 * @property int $id
 * @property int $college_id
 * @property int $stream_id
 * @property int $degree_id
 * @property int $city_id
 * @property int $state_id
 * @property int $position
 * @property int $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property City $city
 * @property College $college
 * @property Degree $degree
 * @property State $state
 * @property Stream $stream
 */
class PopularCollege extends \yii\db\ActiveRecord
{

    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;


    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'popular_college';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['college_id', 'stream_id', 'degree_id', 'state_id'], 'required'],
            [['college_id', 'stream_id', 'degree_id', 'city_id', 'state_id', 'position', 'status'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['city_id'], 'exist', 'skipOnError' => true, 'targetClass' => City::class, 'targetAttribute' => ['city_id' => 'id']],
            [['college_id'], 'exist', 'skipOnError' => true, 'targetClass' => College::class, 'targetAttribute' => ['college_id' => 'id']],
            [['degree_id'], 'exist', 'skipOnError' => true, 'targetClass' => Degree::class, 'targetAttribute' => ['degree_id' => 'id']],
            [['state_id'], 'exist', 'skipOnError' => true, 'targetClass' => State::class, 'targetAttribute' => ['state_id' => 'id']],
            [['stream_id'], 'exist', 'skipOnError' => true, 'targetClass' => Stream::class, 'targetAttribute' => ['stream_id' => 'id']],
            [['college_id', 'stream_id', 'degree_id', 'city_id', 'state_id'], 'unique', 'targetAttribute' => ['college_id', 'stream_id', 'degree_id', 'city_id', 'state_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'college_id' => 'College ID',
            'stream_id' => 'Stream ID',
            'degree_id' => 'Degree ID',
            'city_id' => 'City ID',
            'state_id' => 'State ID',
            'position' => 'Position',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[City]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CityQuery
     */
    public function getCity()
    {
        return $this->hasOne(City::class, ['id' => 'city_id']);
    }

    /**
     * Gets query for [[College]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeQuery
     */
    public function getCollege()
    {
        return $this->hasOne(College::class, ['id' => 'college_id']);
    }

    /**
     * Gets query for [[Degree]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\DegreeQuery
     */
    public function getDegree()
    {
        return $this->hasOne(Degree::class, ['id' => 'degree_id']);
    }

    /**
     * Gets query for [[State]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\StateQuery
     */
    public function getState()
    {
        return $this->hasOne(State::class, ['id' => 'state_id']);
    }

    /**
     * Gets query for [[Stream]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\StreamQuery
     */
    public function getStream()
    {
        return $this->hasOne(Stream::class, ['id' => 'stream_id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\PopularCollegeQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\PopularCollegeQuery(get_called_class());
    }
}
