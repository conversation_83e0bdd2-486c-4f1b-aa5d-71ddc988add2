<?php

namespace common\services;

use common\models\Faq;
use common\models\old\GmuFaq;
use stdClass;
use yii\helpers\Json;
use common\models\FilterPageSeo;

class FaqService
{
    public function getPageLevelFaqDetails($entity, $entityId, $page = null, $childSubPage = null)
    {
       
        if (strtolower($entity == 'filter')) {
            $seoData = FilterPageSeo::find()->select(['id', 'entity'])->where(['slug' => $page])
                ->andWhere(['status' => 1])
                ->one();
            if (empty($seoData)) {
                return [];
            }
            $data = Faq::find()->select(['qnas'])
                ->where(['entity' => $entity])
                ->andWhere(['entity_id' => $seoData->id])
                ->andWhere(['status' => 1]);
        } else {
            $data = Faq::find()->select(['qnas'])
                ->where(['entity' => $entity])
                ->andWhere(['entity_id' => $entityId])
                ->andWhere(['status' => 1]);
        }
        if (strtolower($entity != 'filter')) {
            if (!empty($page) && !empty($childSubPage)) {
                $data->andWhere(['sub_page' => $page, 'child_sub_page' => $childSubPage]);
            } else if (!empty($page)) {
                $data->andWhere(['sub_page' => $page, 'child_sub_page' => null]);
            }
        }
        $data = $data->one();
        if (!empty($data->qnas)) {
            $data =  Json::decode(Json::encode($data->qnas), false);
        }
       
        return $data ?? [];
    }
}
