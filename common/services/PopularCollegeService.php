<?php

namespace common\services;

use common\helpers\CollegeHelper;
use common\helpers\DataHelper;
use common\models\College;
use common\models\FeatureValue;
use common\models\Stream;
use yii\db\Query;

class PopularCollegeService
{
    const MAX_CARDS = 5;

    /**
     * Main method to get college cards based on stream_id, degree_id (level), and state_id
     *
     * @param int|null $streamId
     * @param int|null $degreeId (level)
     * @param int|null $stateId
     * @param int $limit
     * @return array
     */
    public static function getCollegeCards($streamId = null, $degreeId = null, $stateId = null, $limit = self::MAX_CARDS)
    {
        $state_id = self::getUserStateFromIP();

        $stateIdVal = !empty($state_id) && !empty($state_id['stateId']) ? $state_id['stateId'] : $stateId;

        // Get colleges from popular_college table
        if (!empty($streamId) || !empty($degreeId) || !empty($stateIdVal)) {
            $colleges = self::getCollegesFromPopularTable($streamId, $degreeId, $stateIdVal, $limit);
        } else {
            $colleges['colleges'] = [];
        }
        
        if (empty($colleges['colleges'])) {
            // Fallback to default colleges if no matches found
            $colleges['colleges'] = self::getDefaultColleges($limit);
            $colleges['stream_name'] = '';
        }

        return self::formatCollegeData($colleges);
    }

    public static function getCollegeEntityCards($collegeId, $stateId = null)
    {
        $state_id = self::getUserStateFromIP();

        $stateIdVal = !empty($state_id) && !empty($state_id['stateId']) ? $state_id['stateId'] : $stateId;

        $query = (new \yii\db\Query())
            ->select([
                'c.stream_id',
                'COUNT(cc.course_id) AS course_count',
            ])
            ->from(['cc' => 'college_course'])
            ->innerJoin(['c' => 'course'], 'c.id = cc.course_id')
            ->where([
                'cc.college_id' => $collegeId,
            ])
            ->andWhere(['IS NOT', 'c.stream_id', null])
            ->andWhere(['<>', 'c.stream_id', 0])
            ->groupBy('c.stream_id')
            ->orderBy(['course_count' => SORT_DESC, 'c.stream_id' => SORT_ASC])
            ->limit(1);

        $streamId = $query->one();

        return self::getCollegeCards(empty($streamId) ? null : $streamId['stream_id'], null, $stateIdVal, self::MAX_CARDS);
    }

    /**
     * Get colleges from popular_college table based on stream, degree (level), and state
     */
    public static function getCollegesFromPopularTable($streamId, $degreeId, $stateId, $limit)
    {
        $streamName = Stream::find()->select(['name'])->where(['id' => $streamId])->one();

        $query = (new Query())
            ->select([
                'college.id',
                'college.display_name',
                'college.name',
                'college.slug',
                'college.logo_image',
                'college.cover_image',
                'city.name as city_name',
            ])
            ->from('popular_college')
            ->innerJoin('college', 'college.id = popular_college.college_id')
            ->innerJoin('city', 'city.id = college.city_id')
            ->where(['popular_college.status' => 1, 'college.status' => 1]);


        if (!empty($streamId)) {
            $query->andWhere(['popular_college.stream_id' => $streamId]);
        }

        if (!empty($degreeId)) {
            $query->andWhere(['popular_college.degree_id' => $degreeId]);
        }

        if (!empty($stateId)) {
            $query->andWhere(['popular_college.state_id' => $stateId]);
        }

        $query->orderBy(['popular_college.created_at' => SORT_DESC])->limit($limit);

        $colleges['colleges'] = $query->all();
        $colleges['stream_name'] = $streamName->name ?? '';

        return $colleges;
    }

    /**
     * Get user's state from IP address
     */
    public static function getUserStateFromIP()
    {
        $userStateId = null;
        $location = DataHelper::getUserLocation();
        if (!empty($location['stateId'])) {
            $userStateId = $location['stateId'];
        }

        return $userStateId;
    }

    /**
     * Get default colleges when no matches found
     */
    public static function getDefaultColleges($limit)
    {
        return College::find()
            ->select([
                'college.id',
                'college.display_name',
                'college.name',
                'college.slug',
                'college.logo_image',
                'college.cover_image',
                'city.name as city_name'
            ])
            ->innerJoin('city', 'city.id = college.city_id')
            ->where(['college.status' => College::STATUS_ACTIVE])
            ->andWhere(['college.is_popular' => College::POPULAR_YES])
            ->orderBy(new \yii\db\Expression('RAND()')) // random order
            ->limit($limit)
            ->asArray()
            ->all();
    }

    /**
     * Format college data for template display
     */
    public static function formatCollegeData($colleges)
    {
        $formattedColleges = [];

        foreach ($colleges['colleges'] as $college) {
            $type = self::getInstitutionTypeValue($college['id']);
            $categoryRating = (new ReviewService())->getCollegeBasedCategoryRating($college['id']);
            $totalRating = !empty($categoryRating) && CollegeHelper::getTotalRating($categoryRating) > 0 ? CollegeHelper::getTotalRating($categoryRating) : '';

            $formattedColleges['colleges'][] = [
                'id' => $college['id'],
                'name' => !empty($college['display_name']) ? $college['display_name'] : $college['name'],
                'slug' => $college['slug'],
                'logo_image' => $college['logo_image'],
                'cover_image' => $college['cover_image'],
                'city_name' => $college['city_name'],
                'type' => $type,
                'categoryRating' => $totalRating,
                'shortlisted_count' => self::getShortlistedCount($college['id']),
                'nirf_rank' => self::getOverallNirfRank($college['id']),
            ];
        }
        $formattedColleges['stream_name'] = $colleges['stream_name'] ?? '';

        return $formattedColleges;
    }

    public static function getInstitutionTypeValue($collegeId)
    {
        return (new Query())
            ->select(['fv.value'])
            ->from('college_feature_value cfv')
            ->innerJoin('feature_value fv', 'fv.id = cfv.feature_value_id')
            ->innerJoin('feature f', 'f.id = fv.feature_id')
            ->where([
                'cfv.college_id' => $collegeId,
                'fv.status'      => FeatureValue::STATUS_ACTIVE,
                'f.name'         => 'Institution Type',
            ])
            ->scalar() ?: '';
    }

    public static function getOverallNirfRank($collegeId)
    {
        return (new Query())
            ->select(['cr.rank'])
            ->from('college_rankings cr')
            ->where([
                'cr.college_id' => $collegeId,
                'cr.publisher_id' => 4, //'nirf',
                'cr.criteria_id' => 10, //'overall'
                'cr.year' => date('Y'),
            ])
            ->scalar() ?: null;
    }

    public static function getShortlistedCount($collegeId)
    {
        return (new Query())
            ->select(['count(*)'])
            ->from('student_college_shortlist')
            ->where([
                'college_id' => $collegeId,
            ])
            ->scalar() ?: null;
    }
}
