<?php

namespace console\controllers;

use common\models\Review;
use common\models\ReviewAnswer;
use common\models\ReviewCategory;
use common\models\ReviewContent;
use common\models\CollegeContent;
use common\models\ReviewImage;
use common\event\ReviewFilterEvent;
use Yii;
use yii\console\Controller;
use Exception;
use yii\helpers\ArrayHelper;

class ReviewApprovalController extends Controller
{

    public function actionGetReviews()
    {
        $reviewContentArr = [];
        $reviewCategory =  ArrayHelper::map(ReviewCategory::find()->select(['id', 'name'])->where(['status' => 1])->all(), 'id', 'name');

        $reviews = Review::find()->where(['status' => Review::STATUS_PENDING])->all();
        // ->andWhere(['id' => 57766])->all();
        foreach ($query->batch() as $reviews) {
            // dd($reviews);
            foreach ($reviews as $review) {
                unset($reviewContentArr);
                $reviewContent = ReviewContent::find()->where(['review_id' => $review->id])->all();
                // ->andWhere(['status' => ReviewContent::STATUS_PENDING])->all();
                // dd($reviewContent);
                if(empty($reviewContent)) {
                    continue;
                }
                
                foreach ($reviewContent as $content) {

                    $responseData = $this->chatgptFilteringProcess($content->content, $reviewCategory[$content->review_category_id]);
                    $yesItems = [];

                    foreach ($responseData as $key => $value) {
                        if (strtolower($key) === 'sms_language') {
                            continue;
                        }

                        if (in_array(strtolower((string)$value), ['yes', '1', 'true'], true)) {
                            $yesItems[] = $key;
                        }
                    }

                    if (!empty($yesItems)) {
                        echo "Review content Rejected \t {$content->id} \n";
                        continue;
                    } else {
                        echo "Approving  content \t {$content->id} \n";

                        $command = \Yii::$app->db->createCommand('UPDATE review_content SET status=:status WHERE id=:id')
                        ->bindValue(':id', $content->id)
                        ->bindValue(':status', ReviewContent::STATUS_APPROVED)->execute();
                        // dd($command);
                        if ($command) {
                            $reviewContentArr[$content->id] = $content->id;
                        }
                    }
                }
                // dd($reviewContentArr);
                if (empty($reviewContentArr)) {
                    echo "Review Rejected \t {$review->id} \n";
                    continue;
                } else {

                    if (count($reviewContentArr) >= 2) {

                        $commandReview = \Yii::$app->db->createCommand('UPDATE review SET status=:status, updated_at=:updated_at WHERE id=:id')
                        ->bindValue(':id', $review->id)
                        ->bindValue(':status', ReviewContent::STATUS_APPROVED)
                        ->bindValue(':updated_at', date('Y-m-d h:i:s'))->execute();
  
                        if($commandReview) {
                            $this->approvAnswer($review);
                            $this->approvImages($review);

                            (new ReviewFilterEvent())->updateReviewElastic($review);
                            $this->updateCollege($review);

                        }
                    }
                }
            }
        }
    }

    public function updateCollege(Review $review)
    {

        $collegeContent = CollegeContent::find()
            ->where(['entity_id'=> $review->college->id])
            ->andWhere(['sub_page' => 'reviews'])
            ->one();

            if(empty($collegeContent)) {
                return false;
            }

            $commandReview = \Yii::$app->db->createCommand('UPDATE college_content SET updated_at=:updated_at, status=:status WHERE id=:id')
                        ->bindValue(':id', $collegeContent->id)
                        ->bindValue(':status', 1)
                        ->bindValue(':updated_at', date('Y-m-d h:i:s'))->execute();

    }

    public function approvAnswer($review)
    {
        $reviewAns = ReviewAnswer::find()->where(['review_id' => $review->id])->andWhere(['status' => ReviewAnswer::STATUS_PENDING])->all();
        foreach ($reviewAns as $value) {
            if (empty($value->answer)) {
                continue;
            }

            $value->status  = ReviewAnswer::STATUS_APPROVED;
            $value->save();
        }
    }

    public function approvImages($review)
    {
        $reviewAns = ReviewImage::find()->where(['review_id' => $review->id])->andWhere(['status' => ReviewAnswer::STATUS_PENDING])->all();
        foreach ($reviewAns as $value) {
            if (empty($value->file)) {
                continue;
            }

            $value->status  = ReviewAnswer::STATUS_APPROVED;
            $value->save();
        }
    }

    public function chatgptFilteringProcess($description, $title)
    {
        $prompt =
            "You are a content moderator for an educational review platform. Your task is to analyze student reviews for inappropriate or non-compliant content. Evaluate the provided review text against the given criteria and respond with following review categories as keys and a boolean as their values.
            Review Categories to Check:

            profanity: Curse words, swearing, vulgar language, or inappropriate phrases and their common abbreviations including hindi/indian abuses
            hate_speech: Discriminatory, hostile, or threatening content targeting individuals or groups
            self_harm: Content promoting or discussing suicide, self-injury, or harmful behavior
            sexual_content: Sexually explicit material, inappropriate references, or content unsafe for minors
            violence: Threats, promotion of violence, or hostile content targeting individuals or communities
            self_promotion: Personal advertising or self-referential content(instagram/youtube link/social media etc.) Reviewing or praising college admin/professors is allowed
            question_format: Queries or questions instead of review content
            non_verbal: Excessive use of special characters, emojis, or numbers
            sms_language: Text message abbreviations, informal shorthand, or dated internet slang

            Instructions:

            Check each category carefully
            If the content fits the definition of a category, the value of that category should be boolean 'true'
            Respond ONLY with the JSON format shown below
            Do not include explanations or additional text

            Required Output Format:
            {
                \"profanity\": true/false,
                \"hate_speech\": true/false,
                \"self_harm\": true/false,
                \"sexual_content\": true/false,
                \"violence\": true/false,
                \"self_promotion\": true/false,
                \"question_format\": true/false,
                \"non_verbal\": true/false,
                \"sms_language\": true/false
            }

            //REVIEW_CONTENT_START

            {$title}: {$description}
            //REVIEW_CONTENT_END";
        //    dd($prompt);
        try {
            $url = Yii::$app->params['AZURE_ENDPOINT'] . 'openai/deployments/' . Yii::$app->params['MODEL_NAME'] . '/chat/completions?api-version=' . Yii::$app->params['AZURE_API_VERSION'];
            // dd($url);
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . Yii::$app->params['AZURE_API_KEY']
            ]);
            
            $data = [

                'messages' => [
                    ['role' => 'user', 'content' => $prompt]
                ],
                'temperature' => 0,

            ];

            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                throw new Exception(curl_error($ch));
            }
            curl_close($ch);

            $responseData = json_decode($response, true);
            // dd($responseData);
            if (isset($responseData['choices'][0]['message']['content'])) {
                $jsonOutput = $responseData['choices'][0]['message']['content'];
                return json_decode($jsonOutput, true);
            } else {
                throw new Exception('Invalid response from OpenAI: ' . $response);
            }
        } catch (Exception $e) {
            Yii::error('Error occurred during ChatGPT moderation: ' . $e->getMessage(), __METHOD__);
            return null;
        }
    }
}
