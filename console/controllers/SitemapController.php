<?php

namespace console\controllers;

use Carbon\Carbon;
use common\event\SitemapEvent;
use common\event\SitemapEventNew;
use common\helpers\CollegeHelper;
use common\models\Article;
use common\models\Board;
use common\models\CareerContent;
use common\models\Category;
use common\models\College;
use common\models\CollegeCourseContent;
use common\models\CollegeProgram;
use common\models\Course;
use common\models\documents\Sitemap;
use common\models\ExamContent;
use common\models\News;
use common\models\NewsCategory;
use common\models\NewsContentSubdomain;
use common\models\NewsSubdomain;
use common\models\NewsSubdomainLiveUpdate;
use common\models\OlympiadContent;
use common\models\Scholarship;
use common\models\ScholarshipContent;
use common\models\SitemapUpdate;
use common\services\SitemapService;
use common\services\v2\NewsService;
use Exception;
use frontend\helpers\Url;
use Yii;
use yii\console\Controller;
use yii\console\widgets\Table;

class SitemapController extends Controller
{

    const DOMAIN = 'https://www.getmyuni.com';

    const PER_PAGE_RESULT = 20;

    const CHANGE_FREQ_ALWAYS = 'always';
    const CHANGE_FREQ_HOURLY = 'hourly';
    const CHANGE_FREQ_DAILY = 'daily';
    const CHANGE_FREQ_WEEKLY = 'weekly';
    const CHANGE_FREQ_MONTHLY = 'monthly';
    const CHANGE_FREQ_YEARLY = 'yearly';
    const CHANGE_FREQ_NEVER = 'never';
    const SITEMAP_HEADER = '<?xml version="1.0" encoding="utf-8"?>' . '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
    const GOOGLE_SITEMAP_HEADER = '<?xml version="1.0" encoding="utf-8"?>' . '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1" xmlns:news="http://www.google.com/schemas/sitemap-news/0.9">';

    protected $pagination = true;
    protected $page = 1;
    protected $sitemapService;
    protected $newsService;

    public function __construct(
        $id,
        $module,
        SitemapService $sitemapService,
        NewsService $newsService,
        $config = []
    ) {

        $this->sitemapService = $sitemapService;
        $this->newsService = $newsService;
        parent::__construct($id, $module, $config);
    }

    public function actionGenerate()
    {
        $this->actionNewsSubdomain();
        $this->actionNewsSubdomainContent();
        $this->actionNewsSubdomainLiveUpdate();
        $this->actionCareers();
        $this->actionOlympiad();
        $this->actionScholarship();
        $this->actionArticles();
        $this->actionCourses();
        $this->actionExams();
        $this->actionBoards();
        $this->actionColleges();
    }

    public function actionGenerateSitemap()
    {
        $this->generateArticlesFile();
        $this->generateScholarshipFile();
        $this->generateCareersFile();
        $this->generateOlympiadFile();
        $this->generateExamsFile();
        $this->generateCourseFile();
        $this->generateBoardFile();
        $this->generateCollegesFile();
        $this->generateCollegeCIPIFile();
    }


    private function persistToCollection($data, $entity, $domain, $frequency, $priority = '0.6')
    {
        $model = Sitemap::find()
            ->where(['slug' => $data->slug])
            ->andWhere(['entity' => $entity])
            ->one();

        if (!$model) {
            $model = new Sitemap();
        }

        $model->entity = $entity;
        $model->domain = $domain;
        $model->slug = $data->slug;
        $model->subPage = $data->page ?? '';
        $model->priority = $priority;
        $model->changeFreq = $frequency;
        $model->lastModified = $data->modified ?? '';
        $model->createdDate = $data->date ?? '';
        $model->status = $data->status ?? Sitemap::STATUS_ACTIVE;
        if (!empty($data->title)) {
            $model->title = $data->title;
        } else {
            return '';
        }


        try {
            $model->save();
            echo "{$model->slug} \t {$model->entity} \n";
        } catch (Exception $e) {
            print_r($e->getMessage());
        }
    }

    public function generateExamsFile()
    {
        $redirectSlugs = $this->getRedirectSlugs();

        $postData = SitemapUpdate::find()->select(['slug', 'sitemap_update', 'change_freq', 'priority'])
            ->where(['entity' => 'exams'])
            ->andWhere(['status' => 1])
            ->andWhere(['not in', 'slug', $redirectSlugs])
            ->orderBy(['sitemap_update' => SORT_DESC])
            ->all();

        $postSitemap = $this->getMysqlXmlString($postData, Sitemap::ENTITY_EXAM);

        try {
            file_put_contents(Yii::getAlias('@newsSitemapPath') . '/exam-sitemap.xml', $postSitemap);
        } catch (\Throwable $th) {
            print_r($th->getMessage());
        }
    }

    public function generateArticlesFile()
    {
        $redirectSlugs = $this->getRedirectSlugs();

        $postData = SitemapUpdate::find()->select(['slug', 'sitemap_update', 'change_freq', 'priority'])
            ->where(['entity' => 'articles'])
            ->andWhere(['status' => 1])
            ->andWhere(['not in', 'slug', $redirectSlugs])
            ->orderBy(['sitemap_update' => SORT_DESC])
            ->all();

        $postSitemap = $this->getMysqlXmlString($postData, Sitemap::ENTITY_ARTICLE);

        try {
            file_put_contents(Yii::getAlias('@newsSitemapPath') . '/article-sitemap.xml', $postSitemap);
        } catch (\Throwable $th) {
            print_r($th->getMessage());
        }
    }


    public function generateCourseFile()
    {
        $redirectSlugs = $this->getRedirectSlugs();

        $postData = SitemapUpdate::find()->select(['slug', 'sitemap_update', 'change_freq', 'priority'])
            ->where(['entity' => 'courses'])
            ->andWhere(['status' => 1])
            ->andWhere(['not in', 'slug', $redirectSlugs])
            ->orderBy(['sitemap_update' => SORT_DESC])
            ->all();

        $postSitemap = $this->getMysqlXmlString($postData, 'courses');

        try {
            file_put_contents(Yii::getAlias('@newsSitemapPath') . '/course-sitemap.xml', $postSitemap);
        } catch (\Throwable $th) {
            print_r($th->getMessage());
        }
    }


    public function generateCollegesFile()
    {
        // $redirectSlugs = $this->getRedirectSlugs();

        $arr = ['images-videos', 'qna', 'compare-college'];
        $renameFile = ['admission' => 'admission-college', 'placements' => 'college-placement',  'courses-fees' => 'courseFees'];

        $pages = array_diff(['info', 'admission', 'courses-fees', 'placements', 'facilities', 'scholarships', 'cut-off', 'reviews', 'qna', 'result', 'ranking', 'news', 'verdict', 'syllabus', 'hostel'], $arr);

        foreach (array_values($pages) as $page) {
            $postData = SitemapUpdate::find()->select(['slug', 'sitemap_update', 'change_freq', 'priority'])
                ->where(['entity' => 'college'])
                ->andWhere(['sub_page' => $page])
                ->andWhere(['status' => 1])
                // ->andWhere(['not in', 'slug', $redirectSlugs])
                ->orderBy(['sitemap_update' => SORT_DESC])
                ->all();

            if (empty($postData)) {
                return false;
            }

            $postSitemap = $this->getMysqlXmlString($postData, 'college');

            $file = !empty($renameFile[$page]) ? '/' . $renameFile[$page] : '/college-' . $page;

            try {
                file_put_contents(Yii::getAlias('@newsSitemapPath') . $file . '-sitemap.xml', $postSitemap);
            } catch (\Throwable $th) {
                print_r($th->getMessage());
            }
        }
    }

    public function generateCollegeCIPIFile()
    {
        $arr = ['ci', 'pi'];
        // $redirectSlugs = $this->getRedirectSlugs();

        foreach ($arr as $page) {
            $postData = SitemapUpdate::find()->select(['slug', 'sitemap_update', 'change_freq', 'priority'])
                ->where(['entity' => 'college'])
                ->andWhere(['sub_page' => $page])
                ->andWhere(['status' => 1])
                // ->andWhere(['not in', 'slug', $redirectSlugs])
                ->orderBy(['sitemap_update' => SORT_DESC])
                ->all();
            if (empty($postData)) {
                return false;
            }

            $arrayData = array_chunk($postData, 30000);
            $pathArr = [];

            foreach ($arrayData as $key => $value) {
                $postSitemap = $this->getMysqlXmlString($value, 'college');

                $pathArr[] = '/college-' . $page . $key . '-sitemap.xml';

                try {
                    file_put_contents(Yii::getAlias('@newsSitemapPath') . '/college-' . $page . $key . '-sitemap.xml', $postSitemap);
                } catch (\Throwable $th) {
                    print_r($th->getMessage());
                }
            }
            $dataFile = $this->generateFile($pathArr);
            file_put_contents(Yii::getAlias('@newsSitemapPath') . '/college-' . $page . '-sitemap.xml', $dataFile);
        }
    }

    public function generateFile($response)
    {
        $postSitemap = self::SITEMAP_HEADER;

        foreach ($response as $post) {
            // dd($post);
            $postSitemap .= '<url>';
            $postSitemap .= '<loc>' . self::DOMAIN . $post . '</loc>';
            $postSitemap .= '</url>';
        }

        $postSitemap .= '</urlset>';

        return $postSitemap;
    }


    public function generateBoardFile()
    {
        $redirectSlugs = $this->getRedirectSlugs();

        $postData = SitemapUpdate::find()->select(['slug', 'sitemap_update', 'change_freq', 'priority'])
            ->where(['entity' => 'boards'])
            ->andWhere(['status' => Sitemap::STATUS_ACTIVE])
            ->andWhere(['not in', 'slug', $redirectSlugs])
            ->orderBy(['sitemap_update' => SORT_DESC])
            ->all();

        $postSitemap = $this->getMysqlXmlString($postData, 'boards');

        try {
            file_put_contents(Yii::getAlias('@newsSitemapPath') . '/board-sitemap.xml', $postSitemap);
        } catch (\Throwable $th) {
            print_r($th->getMessage());
        }
    }

    public function generateCareersFile()
    {
        $redirectSlugs = $this->getRedirectSlugs();

        $postData = SitemapUpdate::find()->select(['slug', 'sitemap_update', 'change_freq', 'priority'])
            ->where(['entity' => 'careers'])
            ->andWhere(['status' => 1])
            ->andWhere(['not in', 'slug', $redirectSlugs])
            ->orderBy(['sitemap_update' => SORT_DESC])
            ->all();

        $postSitemap = $this->getMysqlXmlString($postData, Sitemap::ENTITY_CAREER);

        try {
            file_put_contents(Yii::getAlias('@newsSitemapPath') . '/career-sitemap.xml', $postSitemap);
        } catch (\Throwable $th) {
            print_r($th->getMessage());
        }
    }

    public function generateOlympiadFile()
    {
        $redirectSlugs = $this->getRedirectSlugs();

        $postData = SitemapUpdate::find()->select(['slug', 'sitemap_update', 'change_freq', 'priority'])
            ->where(['entity' => 'olympiad'])
            ->andWhere(['status' => 1])
            ->andWhere(['not in', 'slug', $redirectSlugs])
            ->orderBy(['sitemap_update' => SORT_DESC])
            ->all();

        $postSitemap = $this->getMysqlXmlString($postData, Sitemap::ENTITY_CAREER);

        try {
            file_put_contents(Yii::getAlias('@newsSitemapPath') . '/olympiad-sitemap.xml', $postSitemap);
        } catch (\Throwable $th) {
            print_r($th->getMessage());
        }
    }

    /*** Generate ScholarShip XML File**/
    public function generateScholarshipFile()
    {

        $redirectSlugs = $this->getRedirectSlugs();

        $postData = SitemapUpdate::find()
            ->where(['entity' => SitemapUpdate::ENTITY_SCHOLARSHIP])
            ->andWhere(['status' => SitemapUpdate::STATUS_ACTIVE])
            ->andWhere(['not in', 'slug', $redirectSlugs])
            ->orderBy(['sitemap_update' => SORT_DESC])
            ->all();

        $postSitemap = $this->getMysqlXmlString($postData, SitemapUpdate::ENTITY_SCHOLARSHIP);

        try {
            file_put_contents(Yii::getAlias('@newsSitemapPath') . '/scholarship-sitemap.xml', $postSitemap);
        } catch (\Throwable $th) {
            print_r($th->getMessage());
        }
    }

    public function getMysqlXmlString($response, $entity)
    {
        $postSitemap = self::SITEMAP_HEADER;

        foreach ($response as $post) {
            // dd($post);
            $postSitemap .= '<url>';
            $postSitemap .= '<loc>' . self::DOMAIN . '/' . $post->slug . '</loc>';
            if (isset($post->sitemap_update)) {
                $postSitemap .= '<lastmod>' . date(DATE_ATOM, strtotime($post->sitemap_update)) . '</lastmod>';
            }
            $postSitemap .= '<changefreq> ' . $post->change_freq . '</changefreq>';
            $postSitemap .= '<priority>' . $post->priority . '</priority>';
            $postSitemap .= '</url>';
        }

        $postSitemap .= '</urlset>';

        return $postSitemap;
    }

    public function generateGoogleNewsFile()
    {
        $date = Carbon::now()->subDay(2)->toDateTimeLocalString();
        $collection = Yii::$app->mongodb->getCollection('sitemap');
        $postData = $collection->aggregate([
            [
                '$match' =>
                [
                    'entity' => Sitemap::ENTITY_NEWS,
                    'lastModified' =>
                    [
                        '$gt' => $date
                    ],
                    'status' => Sitemap::STATUS_ACTIVE
                ]
            ],
            [
                '$sort' => ['createdDate' => -1]
            ]
        ]);

        $postSitemap = $this->getGoogleXmlString($postData, Sitemap::ENTITY_NEWS);

        try {
            file_put_contents(Yii::getAlias('@newsSitemapPath') . '/google-news-sitemap.xml', $postSitemap);
        } catch (\Throwable $th) {
            print_r($th->getMessage());
        }
    }

    public function getXmlString($response, $entity)
    {
        $postSitemap = self::SITEMAP_HEADER;

        foreach ($response as $post) {
            // dd($post);
            $postSitemap .= '<url>';
            $postSitemap .= '<loc>' . self::DOMAIN . '/' . $entity . '/' . $post->slug . '</loc>';
            if (isset($post->lastModified)) {
                $postSitemap .= '<lastmod>' . date(DATE_ATOM, strtotime($post->lastModified)) . '</lastmod>';
            }
            $postSitemap .= '<changefreq> ' . $post->changeFreq . '</changefreq>';
            $postSitemap .= '<priority>' . $post->priority . '</priority>';
            $postSitemap .= '</url>';
        }

        $postSitemap .= '</urlset>';

        return $postSitemap;
    }


    public function getGoogleXmlString($response, $entity)
    {
        $postSitemap = self::GOOGLE_SITEMAP_HEADER;

        foreach ($response as $post) {
            if (empty($post['title']) || empty($post['image']) || empty($post['createdDate'])) {
                continue;
            }
            $postSitemap .= '<url>';
            $postSitemap .= '<loc>' . self::DOMAIN . '/' . $entity . '/' . $post['slug'] . '</loc>';
            $postSitemap .= '<news:news>';
            $postSitemap .= '<news:publication>';
            $postSitemap .= '<news:name>Getmyuni</news:name>';
            $postSitemap .= '<news:language>en</news:language>';
            $postSitemap .= '</news:publication>';

            if (isset($post['createdDate'])) {
                $postSitemap .= '<news:publication_date>' . date(DATE_ATOM, strtotime($post['createdDate'])) . '</news:publication_date>';
            } else {
                $postSitemap .= '<news:publication_date>' . date(DATE_ATOM, strtotime($post['lastModified'])) . '</news:publication_date>';
            }

            //if (isset($post['title'])) {
            $postSitemap .= '<news:title>' . htmlspecialchars($post['title']) . '</news:title>';
            $postSitemap .= '<news:keywords' . htmlspecialchars($post['keywords']) . '</news:keywords>';
            //}
            $postSitemap .= '</news:news>';
            $postSitemap .= '<lastmod>' . date(DATE_ATOM, strtotime($post['lastModified'])) . '</lastmod>';
            if (isset($post['image'])) {
                $postSitemap .= '<image:image>';
                $postSitemap .= '<image:loc>' . $post['image'] . '</image:loc>';
                $postSitemap .= '</image:image>';
            }
            $postSitemap .= '</url>';
        }

        $postSitemap .= '</urlset>';

        return $postSitemap;
    }

    /*********Scholarship Data in sitemap update********/

    public function actionScholarship()
    {
        $query = ScholarshipContent::find()->where(['status' => 1]);

        foreach ($query->batch() as $scholarships) {
            foreach ($scholarships as $scholarship) {
                (new SitemapEventNew())->updateScholarshipSitemap($scholarship->scholarship_id);
            }
        }
    }

    public function actionCareers()
    {
        $query = CareerContent::find()->where(['status' => 1]);

        foreach ($query->batch() as $careerPages) {
            foreach ($careerPages as $careerPage) {
                (new SitemapEventNew())->generateCareerSitemap($careerPage->career_id);
            }
        }
    }

    public function actionOlympiad()
    {
        $query = OlympiadContent::find()->where(['status' => 1]);

        foreach ($query->batch() as $olympiadPages) {
            foreach ($olympiadPages as $olympiadPage) {
                (new SitemapEventNew())->generateOlympiadSitemap($olympiadPage->olympiad_id);
            }
        }
    }

    public function actionExams()
    {
        $query = ExamContent::find()->with('exam');

        foreach ($query->batch() as $examPages) {
            foreach ($examPages as $examPage) {
                (new SitemapEventNew())->generateExamSitemap($examPage->exam_id);
            }
        }
    }

    public function actionArticleCategory()
    {
        $query = Category::find();

        foreach ($query->batch() as $categories) {
            foreach ($categories as $category) {
                if ($category->id == 27) {
                    continue;
                }

                $data = (object)[];
                $data->slug = $category->slug;
                $data->modified = date(DATE_ATOM, strtotime($category->updated_at));
                $data->status = $category->status;

                $this->persistToCollection($data, Sitemap::ENTITY_ARTICLE, self::DOMAIN, self::CHANGE_FREQ_WEEKLY);
            }
        }
    }

    public function actionColleges()
    {
        $query = College::find()->where(['status' => 1]);

        foreach ($query->batch() as $colleges) {
            foreach ($colleges as $college) {
                (new SitemapEventNew())->generateCollegeSitemap($college->id);
            }
        }
    }

    //pi sitemap
    public function actionUpdateCollegePrograms()
    {
        $query = CollegeProgram::find()
            ->where(['page_index' => 1])
            ->andWhere(['status' => CollegeProgram::STATUS_ACTIVE]);

        foreach ($query->batch() as $programs) {
            foreach ($programs as $program) {
                (new SitemapEventNew())->updateCollegeProgramSitemap($program, $program->college->slug, $program->college->status);
            }
        }
    }

    //ci sitemap
    public function actionUpdateCollegeCourses()
    {
        $query = CollegeCourseContent::find()
            ->where(['page_index' => 1])
            ->andWhere(['status' => CollegeCourseContent::STATUS_ACTIVE]);

        foreach ($query->batch() as $collegeCourses) {
            foreach ($collegeCourses as $collegeCourse) {
                (new SitemapEventNew())->updateCollegeCourseSitemapMysl($collegeCourse, $collegeCourse->college->slug, $collegeCourse->college->status);
                // (new SitemapEvent())->updateCollegeCourseSitemap($collegeCourse, $collegeCourse->college->slug, $collegeCourse->college->status);
            }
        }
    }

    public function actionBoards()
    {
        $query = Board::find()->where(['status' => 1]);

        foreach ($query->batch() as $boards) {
            foreach ($boards as $board) {
                (new SitemapEventNew())->generateBoardSitemap($board->id);
                // (new SitemapEvent())->updateBoardUpdateXml($board->id, $board->status);
            }
        }
    }

    public function actionCourses()
    {
        $query = Course::find()->where(['status' => 1]);

        foreach ($query->batch() as $courses) {
            foreach ($courses as $course) {
                (new SitemapEventNew())->generateCourseSitemap($course->id);
            }
        }
    }

    public function actionUpdateListingStatus()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'listing_slug.csv';

        $row = 0;
        $emptyModel = 0;
        $validationFailed = 0;

        if (!file_exists($file)) {
            echo "CSV File dosen't exists!" . PHP_EOL;
            echo 'Path: ' . $file . PHP_EOL;
            exit;
        }

        echo 'Import Started.' . PHP_EOL;

        $failedRecords = [];

        if (($handle = fopen($file, 'r')) !== false) {
            while (($data = fgetcsv($handle, 2000, ',')) !== false) {
                $row++;

                if ($row == 1) {
                    continue;
                }

                try {
                    $model = Sitemap::find()->where(['slug' => $data[0]])->one();
                    if ($model) {
                        $model->status = Sitemap::STATUS_INACTIVE;
                        if ($model->save()) {
                            // echo "{$model->slug} \t {$model->entity} \t";
                        } else {
                            $validationFailed += 1;
                            $data = array_filter($model->attributes, function ($value) {
                                return !is_null($value) && $value !== '';
                            });
                            $data['errors'] = $this->getValidationErrorsAsList($model->errors);
                            $failedRecords[] = $data;
                        }
                    } else {
                        $emptyModel += 1;
                    }

                    echo 'Processing Row ' . $row . ' from CSV. Total empty data Found: ' . $emptyModel . "\r";
                } catch (\Exception $e) {
                    print_r($e);
                    exit;
                }
            }

            $this->consoleTable($failedRecords);
            $row--;
            $data = [['Total Records' => $row, 'Inserted Records' => $row - $validationFailed - $emptyModel, 'Invalid Records' => $validationFailed, 'Duplicate Records' => $emptyModel]];
            $this->consoleTable($data);
            echo 'All done!' . PHP_EOL;

            fclose($handle);
        }
    }

    private function consoleTable($data)
    {

        if (count($data) < 1) {
            return;
        }

        $header = array_keys($data[0]);
        $body = [];
        foreach ($data as $lines) {
            $body[] = array_values($lines);
        }

        $table = new Table();
        echo $table->setHeaders($header)->setRows($body)->run();
        return;
    }


    public function actionArticles()
    {
        $query = Article::find()
            ->select(['id', 'slug', 'lang_code', 'updated_at', 'published_at', 'status'])
            ->andWhere(['not', ['entity' => 'study-abroad']])
            ->andWhere(['not', ['category_id' => 27]])
            ->where(['status' => Article::STATUS_ACTIVE]);

        foreach ($query->batch() as $articles) {
            foreach ($articles as $article) {
                (new SitemapEventNew())->updateArticleSitemap($article->id);
            }
        }
    }

    public function actionNewsSubdomain()
    {
        $query = NewsSubdomain::find()->select(['id', 'slug', 'updated_at', 'published_at', 'status'])->where(['status' => News::STATUS_ACTIVE]);

        foreach ($query->batch() as $news) {
            foreach ($news as $news) {
                (new SitemapEventNew())->generateNewsSitemap($news->id, $news->status, $news->updated_at, $news->published_at);
            }
        }
    }

    public function actionNewsSubdomainContent()
    {
        $query = NewsContentSubdomain::find()->select(['news_id', 'updated_at', 'status'])->where(['status' => News::STATUS_ACTIVE]);

        foreach ($query->batch() as $news) {
            foreach ($news as $news) {
                (new SitemapEventNew())->generateNewsSitemap($news->news_id, $news->status, $news->updated_at);
            }
        }
    }

    public function actionNewsSubdomainLiveUpdate()
    {
        $query = NewsSubdomainLiveUpdate::find()->select(['news_id', 'updated_at', 'status'])->where(['status' => News::STATUS_ACTIVE]);

        foreach ($query->batch() as $news) {
            foreach ($news as $news) {
                (new SitemapEventNew())->generateNewsSitemap($news->news_id, $news->status, $news->updated_at);
            }
        }
    }

    public function getRedirectSlugs()
    {
        $redirectFile = Yii::getAlias('@frontend') . '/web/.redirection';
        $redirectSlugs = [];

        if (file_exists($redirectFile)) {
            $lines = file($redirectFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

            foreach ($lines as $line) {
                $line = trim($line);
                if (strpos($line, '#') === 0) {
                    continue;
                }

                if (preg_match("/rewrite\s+'\\^\/([^']+)\\\$'/i", $line, $matches)) {
                    $slug = $matches[1];
                    $redirectSlugs[] = $slug;
                }
            }
        }

        return $redirectSlugs;
    }
}
