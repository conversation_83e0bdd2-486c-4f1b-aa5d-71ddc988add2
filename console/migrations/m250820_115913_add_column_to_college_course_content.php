<?php

use yii\db\Migration;

/**
 * Class m250820_115913_add_column_to_college_course_content
 */
class m250820_115913_add_column_to_college_course_content extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%college_course_content}}', 'author_id', $this->integer()->defaultValue(null));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250820_115913_add_column_to_college_course_content cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250820_115913_add_column_to_college_course_content cannot be reverted.\n";

        return false;
    }
    */
}
