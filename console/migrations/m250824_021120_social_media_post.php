<?php

use yii\db\Migration;

/**
 * Class m250824_021120_socail_media_post
 */
class m250824_021120_social_media_post extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $options = null;
        if (\Yii::$app->db->getDriverName() === 'mysql') {
            $options = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%social_media_post}}', [
            'id' => $this->primaryKey(),
            'post_section' => $this->integer(),
            'post_id' => $this->integer(),
            'social_media_type' => $this->integer(),
            'title' => $this->string(),
            'description' => $this->text(),
            'hastag' => $this->string(),
            'schedule_at' => $this->dateTime(),
            'published_status' => $this->integer(),
            'error_log' => $this->json(),
            'status' => $this->integer(),
            'created_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_at' => $this->dateTime(),
            'updated_by' => $this->integer(),
            'published_at' => $this->dateTime(),
            'published_by' => $this->integer(),
        ], $options);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%social_media_post}}');
    }
}
